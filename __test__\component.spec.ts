import { enableAutoUnmount, mount } from '../tests/utils/vueTestUtils';
import { afterEach, describe, expect, it } from 'vitest';
import { HatechLayout } from '../src/index';

enableAutoUnmount(afterEach);

describe('templateComponent.vue', () => {
  it('should has template-component class', () => {
    console.log("mount", mount)
    // Arrange
    const wrapper = mount(HatechLayout);
    // Act
    const templateClasses = wrapper.find('.template-component').exists();

    // Assert
    expect(templateClasses).toBeTruthy();
  });
});
