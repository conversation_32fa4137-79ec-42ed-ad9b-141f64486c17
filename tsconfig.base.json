{
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "rootDir": ".",
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "strict": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "emitDeclarationOnly": true,
    "declarationDir": "types",
    "declaration": true,
  },
  "exclude": ["node_modules", "dist"]
}
