<template>
  <div class="h100">
    <div :class="['header_menu', active ? 'active' : '']" @click="onClick">
      <el-badge
        class="header_menu__badge"
        :is-dot="menu.badge && menu.badge > 0"
      >
        <span :class="['header_menu__icon', menu.icon]" :title="menu.name" />
      </el-badge>
      <div class="header_menu__title" v-text="menu.name" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, type PropType } from 'vue-demi'
import type { EventData, HeaderMenuItem } from '../../types/index'

/**
 * 头部菜单项组件
 */
export default defineComponent({
  name: 'HeaderMenu',

  props: {
    // 菜单数据信息
    menu: {
      type: Object as PropType<HeaderMenuItem>,
      default: () => ({}),
    },
    // 是否选中
    active: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
  },
  setup(props, { emit }) {
    /**
     * 点击后
     */
    const onClick = (): void => {
      const eventData: EventData = {
        event: 'onClickHeaderMenu',
        params: {
          menu: props.menu,
        },
      }
      emit('onEvent', eventData)
    }

    return {
      onClick,
    }
  },
})
</script>

<style lang="scss" scoped>
.h100 {
  height: 100%;
}
.header_menu {
  min-width: 32px;
  max-width: 104px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--hatech-base-font-color);
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  padding: 8px;
  box-sizing: border-box;
  border-radius: 2px;
  &:hover {
    background-color: var(--hatech-base-fill-color-normal);
  }
  &.active {
    background-color: var(--hatech-base-fill-color-normal);
  }
}
.header_menu__badge {
  width: 16px;
  height: 16px;
}
.header_menu__title {
  font-size: 12px;
  display: none;
  padding: 0 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: var(--hatech-primary-color);
}
.header_menu__icon {
  color: currentColor;
  width: 16px;
  height: 16px;
  font-size: 16px;
}
</style>
