import * as path from 'node:path'
import { defineConfig } from 'vite'


export const baseBuildConfig = defineConfig({
  server: {
    host: true,
    port: 3000,
  },
  build: {
    outDir: path.resolve(__dirname, `./dist`),
    emptyOutDir: false,
    lib: {
      entry: path.resolve(__dirname, 'src/index.ts'),
      formats: ['es', 'cjs', 'umd'],
      name: '@hatech/layout_unify',
      fileName: (format) => `index.${format}.js`,
    },
    rollupOptions: {
      external: [
        '@hatech/element-ui',
        '@one-public/icons',
        '@hatech/theme',
        'vue',
        '@vue/composition-api',
        '@vue/composition-api/dist/vue-composition-api.mjs',
      ],
      output: {
        globals: {
          vue: 'Vue',
          '@vue/composition-api': 'VueCompositionAPI',
          '@vue/composition-api/dist/vue-composition-api.mjs':
            'VueCompositionAPI',
          '@hatech/element-ui': 'elementUi',
        },
        exports: 'named'
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern',
      },
    },
  },
})
