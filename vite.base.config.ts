/// <reference types="vitest" />
import * as path from 'node:path'
import { defineConfig, UserConfig } from 'vite'
import type { InlineConfig } from 'vitest/node'
import { isVue2, isVue3 } from 'vue-demi'
import DtsPlugin from 'unplugin-dts/vite'

interface VitestConfigExport extends UserConfig {
  test: InlineConfig
}

const outputName = 'index'
export const getSharedPlugins = () => [
   DtsPlugin({
    bundleTypes: true,
    processor: "vue",
  }),
]

console.log('Vue version:', isVue2 ? 'v2' : 'v3')

export const baseBuildConfig = defineConfig({
  server: {
    host: true,
    port: 3000,
  },
  build: {
    outDir: path.resolve(__dirname, `./dist`),
    emptyOutDir: false,
    lib: {
      entry: path.resolve(__dirname, 'src/index.ts'),
      formats: ['es', 'cjs', 'umd'],
      name: '@hatech/layout_unify',
      fileName: (format) => `${outputName}.${format}.js`,
    },
    rollupOptions: {
      external: [
        '@hatech/element-ui',
        '@one-public/icons',
        '@hatech/theme',
        'vue',
        '@vue/composition-api',
        '@vue/composition-api/dist/vue-composition-api.mjs',
      ],
      output: {
        globals: {
          vue: 'Vue',
          '@vue/composition-api': 'VueCompositionAPI',
          '@vue/composition-api/dist/vue-composition-api.mjs':
            'VueCompositionAPI',
          '@hatech/element-ui': 'elementUi',
        },
        exports: 'named'
      },
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    include: ['__test__/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    alias: {
      '@tests/utils': path.resolve(__dirname, `./tests/utils`),
    },
    setupFiles: [path.resolve(__dirname, 'tests/setup.ts')],
    // server.deps.inline
    server: {
      deps: {
        inline: [
          'vue2.7',
          'vue2',
          'vue-demi',
          '@vue/test-utils',
          '@vue/test-utils2',
        ],
      },
    },
    resolveSnapshotPath: (testPath, snapExtension) => {
      return path.join(
        path.join(
          path.dirname(testPath),
          isVue3 ? '__snapshotsV3__' : '__snapshots__',
        ),
        `${path.basename(testPath)}${snapExtension}`,
      )
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern',
      },
    },
  },
} as VitestConfigExport)
