## [1.0.0-beta.2](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/compare/v1.0.0-beta.0...v1.0.0-beta.2) (2025-08-07)

## [1.0.0-beta.1](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/compare/v1.0.0-beta.0...v1.0.0-beta.1) (2025-08-07)

## [1.0.0-beta.0](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.5...v1.0.0-beta.0) (2025-08-07)


### ⚠ BREAKING CHANGES

* - 移除了example目录，使用playground替代
- 组件名称变更：sider -> side
- 依赖更新可能影响现有项目集成

### Features

* 新增移动端适配 ([6646a74](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/commit/6646a7441acfb631a0f52fa57d7b287ef02fa4a4))
* 重构项目架构和组件结构 ([7f750e1](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/commit/7f750e192a895562c562b5de7c41f519000288a7))

### [0.3.6](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.5...v0.3.6) (2025-06-04)


### Features

* 新增移动端适配 ([6646a74](http://10.27.3.166:8888/hatech-web/hatech-web-layout-unify/commit/6646a7441acfb631a0f52fa57d7b287ef02fa4a4))

### [0.3.5](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.4...v0.3.5) (2024-12-31)


### Bug Fixes

* 平台配置按钮按产线菜单控制展示 ([1dedf63](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/1dedf6313cdbcf38f7e7bf7bfd6decc0039eedd8))

### [0.3.4](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.3...v0.3.4) (2024-12-18)


### Bug Fixes

* 修改为组件外部也能修改产线下标 ([3b7f6ae](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/3b7f6aeaffded5a1d55f4e340887bee22a79d910))

### [0.3.3](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.2...v0.3.3) (2024-12-13)


### Bug Fixes

* 修改边框颜色 ([4219259](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/4219259e825b012f1cfb73bb9c7d4b7b47d63649))

### [0.3.2](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.1...v0.3.2) (2024-12-13)


### Bug Fixes

* 使用全局导入的element-ui组件 ([231e50e](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/231e50ecc670d6dab65144d53f96832dae4ddc7a))

### [0.3.1](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.3.0...v0.3.1) (2024-12-13)

### [0.3.0] (2024-11-25)
* 适配4.3.0基座版本新布局，从而对布局组件的样式进行调整

### [0.2.29](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.28...v0.2.29) (2024-08-01)


### Bug Fixes

* 修改如果当前页面不是微前端也可以跳转 ([c7deab6](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/c7deab6590613fb579db236dc4fd6c278490cdc5))

### [0.2.28](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.27...v0.2.28) (2024-07-18)


### Bug Fixes

* 修改导航图标显示 ([29136d1](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/commit/29136d1eca43c85a13cfc27b86f2cdbc50f267c5))

### [0.2.27](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.26...v0.2.27) (2024-03-05)

### [0.2.26](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.24...v0.2.26) (2024-01-16)

### [0.2.25](http://10.1.110.166:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.24...v0.2.25) (2023-11-30)

### Bug Fixes

* 修复菜单数据更新后不渲染更新的问题

### [0.2.24](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.23...v0.2.24) (2023-09-06)


### Bug Fixes

* 设置默认值防止在组件库平台展示页面报错 ([9fb5319](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9fb5319ea5371525864f8884c33d582b62a4fa31))

### [0.2.23](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.23-beta.0...v0.2.23) (2023-08-29)


### Features

* 样式升级 ([6eb9db6](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/6eb9db63e78b9d34a3d652c3f636d29f8bb7e9ee))

### [0.2.23-beta.0](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.22-beta.1...v0.2.23-beta.0) (2023-08-26)

### [0.2.22-beta.1](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.22...v0.2.22-beta.1) (2023-08-26)


### Features

* 树模式下隐藏部分节点 ([55e38cb](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/55e38cbef18137789eccac725a0f987fe4e5e2d9))

### [0.2.22](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.21...v0.2.22) (2023-08-26)


### Features

* 更改菜单结构 ([2497fc2](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/2497fc211c039be2afa5703071ec1e7c9bb8df6b))

### [0.2.21](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.20...v0.2.21) (2023-08-15)


### Features

* 新增log旁边盒子的插槽header_right ([9012b59](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9012b591f31841639872a45f64adec0e0665d5a1))

### [0.2.20](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19...v0.2.20) (2023-08-10)


### Bug Fixes

* 修复没有配置平台配置但是在浮窗中展示了的问题 ([0671d9d](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/0671d9da670ea4664d09c40f36ac0a4421eacf8d))

### [0.2.19](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.6...v0.2.19) (2023-08-05)

### [0.2.19-beta.6](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.5...v0.2.19-beta.6) (2023-08-03)

### [0.2.19-beta.5](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.4...v0.2.19-beta.5) (2023-08-03)

### [0.2.19-beta.4](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.3...v0.2.19-beta.4) (2023-08-03)

### [0.2.19-beta.3](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.2...v0.2.19-beta.3) (2023-08-01)

### [0.2.19-beta.2](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.1...v0.2.19-beta.2) (2023-08-01)

### [0.2.19-beta.1](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.19-beta.0...v0.2.19-beta.1) (2023-08-01)

### [0.2.19-beta.0](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.18-beta.0...v0.2.19-beta.0) (2023-08-01)

### [0.2.18-beta.0](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.17...v0.2.18-beta.0) (2023-07-06)


### Features

* 给layout 添加插槽 ([17302c7](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/17302c759d9863aa3f70fc420e4a9520aded8273))

### [0.2.17](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.17-beta.0...v0.2.17) (2023-03-30)

### [0.2.17-beta.0](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.16...v0.2.17-beta.0) (2022-10-31)


### Features

* 将点击按钮事件传递出去 ([ad0bb30](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/ad0bb302dbcaab939a8569262d929d9b3bb01655))
* 修改进入平台配置就显示返回按钮 ([f36c4a0](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f36c4a0c1b315d8cf9e9d9fce0252a17fb801838))


### Bug Fixes

* 修改菜单分页判断 ([dfbaa2c](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/dfbaa2c7a2a7919877265c8af44ae93a97b0bd6f))
* 修改根据当前页面切换到对应分页 ([2d625ea](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/2d625ea21af96795a307f025939cbae6911b942e))

### [0.2.16](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.15...v0.2.16) (2022-09-22)


### Bug Fixes

* 修改阴影样式 ([f20227c](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f20227c1996eec6970163960726a946f412db211))

### [0.2.15](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.14...v0.2.15) (2022-08-22)


### Features

* 菜单添加滚动条 ([f505279](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f505279c6bcc86f9bf490f316545367cb86704cf))

### [0.2.14](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.13...v0.2.14) (2022-08-19)


### Features

* 菜单搜索忽略大小写 ([c392fc7](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/c392fc7d9b708ccf54008610724d7135fd7c269f))

### [0.2.13](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.12...v0.2.13) (2022-08-15)


### Bug Fixes

* 修改菜单搜索忽略大小写 ([439f0a7](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/439f0a708eccc8701e7f7986a1089c444e3a617d))

### [0.2.12](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.11...v0.2.12) (2022-08-12)

### [0.2.11](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.10...v0.2.11) (2022-08-12)


### Features

* 修改鼠标放上去的颜色以及间距 ([42203bf](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/42203bf736715af7bf2e28be1104827c17e852cd))

### [0.2.10](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.9...v0.2.10) (2022-08-10)


### Features

* 修改解决方案的样式 ([250bf27](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/250bf274806f3072a7384260718087a8d77d4388))

### [0.2.9](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.2.8...v0.2.9) (2022-08-08)


### Bug Fixes

* 修改如果一级菜单里面有子类,页面无法打开 ([248b7f7](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/248b7f7ed566d269b3fd7baf70018eb081f68830))

### [0.2.8](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/compare/v0.1.4...v0.2.8) (2022-08-04)

### Bug Fixes

* 修改面板高度计算错误 ([fb8f159](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/fb8f159c9637cf2ed299b6f68b31bec65ede44c8))
* 修改页面上展示的下标 ([608f6a4](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/608f6a4c15bc16e55341e84627079b1df2ce7fb4))

## 0.2.7 (2022-07-21)

### Style:

- 修改header的logo分割线([`778b74c1`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/778b74c1146fcd17cd6d3c3c75bad9be00c07538)) (by duowenbo)


## 0.2.6 (2022-07-20)

### New feature:

- 解决方案菜单添加hover样式([`31f399a`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/31f399af235fbda4cee00ccb51367a5c15c0b0c3)) (by duowenbo)

### Bugs fixed:

- 修改每次打开解决方案滚动到相应位置([`c8d8adc`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/c8d8adc69215f8e9d5247fc52301eb1385d2f5dc)) (by duowenbo)
- 修改徽标的边框([`7c74138`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/7c74138327219f6bccdb0d99124f8b4f38ad6a96)) (by duowenbo)
- 修改一级菜单的间距([`755cd62`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/755cd62d977ad3168fed2216715bd9d6479a86cc)) (by duowenbo)



## 0.2.5 (2022-07-20)
### Bugs fixed:

- 去除二级菜单的动画属性([`13d538a`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/13d538ac4232e4594c8ad381f7d62ecfb1165dee)) (by duowenbo)
- 修改快捷操作的z-index值([`10d4755`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/10d4755d9a0f8c6b0e6f2bf00d6e1ca1956bf33a)) (by duowenbo)


## 0.2.4 (2022-07-20)
### Bugs fixed:

- 修改菜单的箭头动画([`97ea196`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/97ea1960be589c637e11f7285d8581dbac5e7dac)) (by duowenbo)
- 修改菜单之间的间距([`f84b89b`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f84b89b84b8043a460e70dbf2e25816bd3a56e81)) (by duowenbo)
- 去除解决方案的分割线([`91a7407`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/91a7407daa1a81d04cae4d0bff52dbf34edee922)) (by duowenbo)

## 0.2.3 (2022-07-19)

### Bugs fixed:

- 修改分割线的颜色([`6d82380`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/6d8238034489afe41ac8f89256c74ec364d96f17)) (by duowenbo)
- 修改解决方案点击的样式([`9c17605`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9c17605d69a9e5160304306f74430ae5b2aaf097)) (by duowenbo)
- 修改文字的高度([`9073b12`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9073b125fe3916583f6b74415292e0bc9734ec15)) (by duowenbo)


## 0.2.2 (2022-07-18)

### Bugs fixed:

- 修改搜索的图标([`d2ff828`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/d2ff82816e2ac9c528f5c7825eacd7611fb2068c)) (by duowenbo)
- 修改解决方案高度计算([`e7b0e22`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/e7b0e22b5c54ba61d9b3147119d55019ce1886d2)) (by duowenbo)
- 
## 0.2.1 (2022-07-18)

### New feature:

- 优化用户选择功能([`231e415`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/231e4153fb0b7cfc2c4cd5c18a42bdbb6e2f8626)) (by duowenbo)
- 解决方案添加搜索功能([`72698ea`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/72698eac9a6883d367a9efeb466084a72a20cb6c)) (by duowenbo)
- 优化解决方案功能([`955f05d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/955f05de64c1f51d085de26abaa37e48729adc09)) (by duowenbo)
- 修改一级菜单的样式([`f6def8d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f6def8d1191dbb4d8035c58403ce3db5beab2a7a)) (by duowenbo)

### Bugs fixed:

- 修改菜单的颜色([`032bdc4`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/032bdc4dc473420b1050ef6e5efd564ef9988a23)) (by duowenbo)
- 优化footer([`dc3aa0d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/dc3aa0d1492a0403775ae33fa7ed6fac00003ea5)) (by duowenbo)
- 修改解决方案的内容高度([`d38c76b`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/d38c76b48816e06a0deef312a1a740b47cca41a9)) (by duowenbo)
- 修改header的文字大小([`975f479`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/975f4790df077da3bc1887a7b5ccea77dfb97f08)) (by duowenbo)
- 优化二级菜单栏([`93acdaf`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/93acdafebf3ab99ff64e5374162eed70ceb4b614)) (by duowenbo)

## 0.1.10 (2022-07-08)

### Bugs fixed:

- 修改平台配置标签元素([`bb16cc0`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/bb16cc017cd04a1d8e37b5e9c444889bb4ecc15d)) (by duowenbo)

## 0.1.9 (2022-07-08)

### Bugs fixed:

- 修改侧边栏层级([`542823a`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/542823aae5762b52a35e3c97019f82b971a4c085)) (by duowenbo)
- 修改菜单文字没有显示完整([`6f4a90b`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/6f4a90b8f62641c41f0f866398267715e0753322)) (by duowenbo)

## 0.1.8 (2022-07-08)

### Style:
- 浮动按钮加边框([`f202f07`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/f202f07dd7eb398f17621376481d745cd368db69)) (by duowenbo)

### Bugs fixed:

- 修改图标([`66dc03d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/66dc03d272421685b2432baa7a07610da0bf9de1)) (by duowenbo)
- 修改快捷操作的边框([`e8a4642`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/e8a4642e1ea220d6652d0483c4bf439ae804116d)) (by duowenbo)

## 0.1.7 (2022-07-06)
### Bugs fixed:
- 修改样式([`b5a18bb`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/b5a18bb21fe537a272bd86def475aece3f1fa9fa)) (by duowenbo)
- 修改样式以及图标([`430dbf1`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/430dbf1a38ec538ebf84918b399d1b006fc5f984)) (by duowenbo)
- 修改样式([`9df25f4`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9df25f4c4126b012d26873b0c238070da6c7fccd)) (by duowenbo)
- 添加动画([`b452ab4`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/b452ab40925177ff09d53690c2fd45f6aabeaab1)) (by duowenbo)

## 0.1.6 (2022-06-30)
### Bugs fixed:

- 修改菜单的z-index层级([`073eff5`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/073eff5861d0c8ee9dba96f0ec873c1afab2787b)) (by duowenbo)
## 0.1.5 (2022-06-30)
### Bugs fixed:

- 修改如果是多层级菜单跳转错误([`cf38d2b`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/cf38d2b79e803c536db9a4672b12271787936adf)) (by duowenbo)
  
## 0.1.4 (2022-06-27)
### Bugs fixed:

- 修改解决方案中的平台配置判断([`3fa5476`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/3fa54768f48baf6574905e1e5cb4b0a59a5b8d2c)) (by duowenbo)
  
## 0.1.3 (2022-06-27)
### Bugs fixed:

- 修改选中的时候使用对应的图标([`4769a46`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/4769a464fad6d23d7939343c89c69539fdaf24d9)) (by duowenbo)
  
### Style

- 修改选中样式([`a8f9b6b`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/a8f9b6b814a605e159a69329ba1ba5484e1bca9f)) (by duowenbo)
  
## 0.1.2 (2022-06-22)

### New feature:

- 修改选中的时候使用对应的图标([`11945ea`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/11945eaf71b9fd2fc30f7b4447edd252ce3e42c4)) (by duowenbo)

### Bugs fixed:

- 修改底部区域距离下边的高度([`9258c3d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/9258c3d7ef408b9a36612b3c79f83d9b15838e3a)) (by duowenbo)
- 修改line的高度([`e3d2b72`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/e3d2b72afb7dbaf34bf1d730f20f8935e8437c83)) (by duowenbo)
- 文字超长显示省略号显示title([`5efcc66`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/5efcc66ff4f697f914b852617fd62e0b255008c6)) (by duowenbo)
- 解决布局内容会超过flex1([`b839894`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/b83989428610f52d0c1c22817cd89c8567271d29)) (by duowenbo)
- 修改页面打开没有选中([`18bbe73`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/18bbe73f942a296b6f9c036c03fbc2e4a7f52600)) (by duowenbo)
- 修改点击工具中心之后再点击首页面板浮动;修改默认图标([`0c04e8d`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/0c04e8d79aff6c32205be4fbe17680881f30de52)) (by duowenbo)
- 修改没有产线的时候时候显示了图标([`4ba9609`](http://10.27.2.26:8888/hatech-web/hatech-web-layout-unify/commit/4ba9609c1d1bdf4b4c500e3849b1f21fc9accac3)) (by duowenbo)
- 
## 2022/6/14 v0.1.1
修改样式，删除多余的组件引用
## 2022/6/13 v0.1.0
修改菜单组件
## 2022/4/24 v0.0.31
更改侧边栏选中问题

## 2022/4/24 v0.0.30
1.修改一级导航栏样式，支持DR的工具中心摆放到菜单栏。
2.修复初次打开页面时，一级导航菜单的分页无法正常展示。

## 2022/4/19 v0.0.29
更改容器的背景色及padding的样式

## 2022/4/14 v0.0.28
更改icon的样式

## 2022/4/13 v0.0.27
更改菜单搜索、图标展示样式

## 2022/4/8 v0.0.26
更改产线在菜单栏展示的方式

## 2022/3/29 v0.0.25
更改icon展示形式，并增加依赖@hatech/icon

## 2022/3/29 v0.0.24
增加产线在菜单栏展示功能，实现切换不同的产线则会获得对应的菜单展示。

## 2022/3/25
更改侧边菜单样式，icon尺寸调整，模块背景色设置。
