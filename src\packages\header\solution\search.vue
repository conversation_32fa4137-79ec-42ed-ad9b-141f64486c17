<template>
  <el-input
    v-model.trim="localValue"
    placeholder="搜索"
    :maxlength="100"
    clearable
    suffix-icon="hatech-icon-search"
    class="searchBox"
  />
</template>

<script lang="ts">
import { defineComponent, ref, watch, type PropType } from 'vue-demi'
import type { MenuItem } from '../../../types/'

export default defineComponent({
  name: 'SolutionSearch',
  props: {
    value: {
      type: String,
      default: '',
    },
    menuList: {
      type: Array as PropType<MenuItem[]>,
      default: () => [],
    },
  },
  emits: {
    input: (value: string) => typeof value === 'string',
    search: (menuList: MenuItem[]) => Array.isArray(menuList),
  },
  setup(props, { emit }) {
    // 响应式数据
    const localValue = ref<string>('')
    const localMenuList = ref<MenuItem[]>([])

    // 监听props.value变化
    watch(
      () => props.value,
      (val: string | undefined) => {
        localValue.value = val || ''
      },
      { immediate: true },
    )

    // 监听localValue变化
    watch(localValue, (newVal: string) => {
      inputChange(newVal)
    })

    // 监听props.menuList变化
    watch(
      () => props.menuList,
      (val: MenuItem[] | undefined) => {
        localMenuList.value = JSON.parse(JSON.stringify(val || []))
      },
      { immediate: true, deep: true },
    )

    // 方法定义
    const cleanSearchData = (): void => {
      if (localValue.value.length > 0) {
        emit('input', '')
      }
    }

    const inputChange = (val: string): void => {
      emit('input', val)
      let searchMenuList: MenuItem[] = []

      localMenuList.value.forEach((item: MenuItem) => {
        const childrenList = (item.children || []).filter(
          (childrenItem: MenuItem) => {
            const cnName = childrenItem.cnName || ''
            const isExist =
              val &&
              childrenItem.uiShow === 1 &&
              childrenItem.uiPath &&
              cnName.toLocaleLowerCase().includes(val.toLocaleLowerCase())

            if (isExist) {
              const reg = new RegExp(val, 'i')
              const matchArr = childrenItem.cnName.match(reg)
              if (matchArr) {
                childrenItem.showCnName = childrenItem.cnName.replace(
                  reg,
                  `<span class="hatech_header_search_target_font">${matchArr[0]}</span>`,
                )
              }
            } else {
              childrenItem.showCnName = childrenItem.cnName
            }
            return isExist
          },
        )

        if (childrenList.length > 0) {
          searchMenuList.push(item)
        }
      })

      if (!val) {
        searchMenuList = localMenuList.value
      }

      emit('search', searchMenuList)
    }

    // 返回模板需要的数据和方法
    return {
      localValue,
      localMenuList,
      cleanSearchData,
      inputChange,
    }
  },
})
</script>

<style lang="scss" scoped>
.searchBox {
  width: 220px;
}
</style>
