{"name": "@hatech/layout_unify", "version": "0.3.6", "description": "统一鉴权平台布局组件", "main": "src/index.js", "scripts": {"postinstall": "node scripts/postinstall.js", "dev:2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground dev", "build:2": "vue-demi-switch 2 vue2 && pnpm --filter vue2-playground build", "build": "pnpm type-check && rimraf ./dist && npm run build:2", "switch-test": "tsx tests/scripts/switchVueTestUtils.ts", "test:2": "pnpm switch-test 2 && vue-demi-switch 2 vue2 && vitest run --config ./playground/vue2/vite.config.ts", "test:all": "pnpm run test:2", "type-check": "vue-tsc --declaration --emitDeclarationOnly", "lint": "eslint --max-warnings 0", "release": "easy-release --sb"}, "repository": {"type": "git", "url": "http://***********:8888/hatech-web/hatech-web-layout-unify.git"}, "keywords": [], "author": {"name": "duowb", "email": "<EMAIL>"}, "license": "ISC", "peerDependencies": {"@vue/composition-api": "^1.4.9", "vue": "^2.6.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "dependencies": {"@hatech/element-ui": "3.0.0-beta.3", "@hatech/theme": "^1.0.0", "@one-public/icons": "^0.0.4", "vue-demi": "latest"}, "devDependencies": {"@microsoft/api-extractor": "^7.52.10", "@sxzz/eslint-config": "^7.0.4", "@sxzz/prettier-config": "^2.2.3", "@types/node": "^20.16.5", "@vitejs/plugin-vue2": "^2.3.1", "@vue/compiler-sfc": "^3.5.3", "@vue/composition-api": "^1.7.2", "@vue/language-core": "^3.0.5", "@vue/test-utils": "2.3.2", "@vue/test-utils2": "npm:@vue/test-utils@^1.3.5", "easy-release": "0.0.12", "eslint": "^9.32.0", "fs-extra": "^10.1.0", "jsdom": "^19.0.0", "prettier": "^3.6.2", "rimraf": "^3.0.2", "sass-embedded": "^1.89.2", "tsx": "^3.12.7", "typescript": "^5.5.4", "unplugin-dts": "1.0.0-beta.6", "unplugin-vue2-script-setup": "^0.11.4", "vite": "^5.4.3", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4", "vue-template-compiler": "~2.6.14", "vue-tsc": "^3.0.5", "vue2": "npm:vue@2.6", "vue2.7": "npm:vue@2.7", "vue3": "npm:vue@3"}, "publishConfig": {"registry": "http://***********:4873/"}, "prettier": "@sxzz/prettier-config", "volta": {"node": "20.18.0"}, "pnpm": {"packageExtensions": {"vue-template-compiler": {"peerDependencies": {"vue": "~2.6.14"}}, "@vue/test-utils2": {"peerDependencies": {"vue": "~2.6.14"}}}, "onlyBuiltDependencies": ["@hatech/layout_unify"]}}