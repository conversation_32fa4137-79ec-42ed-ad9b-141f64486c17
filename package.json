{"name": "@hatech/layout_unify", "version": "1.0.0-beta.3", "description": "统一鉴权平台布局组件", "main": "dist/index.umd.js", "module": "dist/index.es.js", "types": "dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}, "files": ["dist", "src"], "scripts": {"dev": "pnpm --filter vue2-playground dev", "build:2": "pnpm --filter vue2-playground build", "build": "rimraf ./dist && pnpm type && npm run build:2", "type": "vue-tsc --declaration --emitDeclarationOnly", "lint": "eslint --max-warnings 0", "release": "easy-release --sb"}, "repository": {"type": "git", "url": "http://***********:8888/hatech-web/hatech-web-layout-unify.git"}, "keywords": [], "author": {"name": "duowb", "email": "<EMAIL>"}, "license": "ISC", "dependencies": {"@hatech/element-ui": "3.0.0-beta.3", "@hatech/theme": "^1.0.0", "@one-public/icons": "^0.0.4"}, "devDependencies": {"@sxzz/eslint-config": "^7.0.4", "@sxzz/prettier-config": "^2.2.3", "@types/node": "^20.16.5", "@vue/compiler-sfc": "^3.5.3", "@vue/composition-api": "^1.7.2", "@vue/language-core": "^3.0.5", "@vue/test-utils": "2.3.2", "@vue/test-utils2": "npm:@vue/test-utils@^1.3.5", "easy-release": "0.0.12", "eslint": "^9.32.0", "prettier": "^3.6.2", "rimraf": "^3.0.2", "sass": "^1.90.0", "typescript": "^4.9.5", "unplugin-vue2-script-setup": "^0.11.4", "vite": "^5.4.3", "vite-plugin-css-injected-by-js": "^3.5.2", "vitest": "^3.2.4", "vue": "2.6.14", "vue-template-compiler": "2.6.14", "vue-tsc": "^0.38.8"}, "publishConfig": {"registry": "http://***********:4873/"}, "prettier": "@sxzz/prettier-config", "volta": {"node": "20.18.0"}}