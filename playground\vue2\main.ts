import Vue from 'vue';
import VueCompositionApi from '@vue/composition-api'
import Theme from '@hatech/theme'
import App from './App.vue';
import Element from '@hatech/element-ui'
import '@hatech/element-ui/lib/theme-chalk/index.css'
import '@one-public/icons/index.css'


Vue.config.productionTip = false;
Vue.use(Element);
Vue.use(VueCompositionApi);
Vue.use(Theme, {
    default: 'white',
});

new Vue({ render: h => h(App as any) }).$mount('#app');
