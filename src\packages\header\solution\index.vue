<template>
  <el-popover
    placement="bottom-start"
    width="960"
    trigger="click"
    :offset="0"
    :value="popoverVisible"
    popper-class="hatech_layout_header_solution_popper"
    :visible-arrow="false"
    :popper-wrapper-class="popperWrapperClass"
    @show="popoverShow"
    @after-enter="popoverAfterEnter"
    @hide="popoverHide"
    @after-leave="PopoverAfterLeave"
  >
    <Solution
      v-if="leftPopoverVisible"
      v-model="selectIndex"
      :line-menus="lineMenus"
      @click-solution="clickSolution"
      @solution-menu-click="$emit('solutionMenuClick', $event)"
    />
    <template #reference>
      <slot />
    </template>
  </el-popover>
</template>

<script lang="ts">
import { defineComponent, ref, type PropType } from 'vue-demi'
import type { LineMenuItem } from '../../../types/'
import Solution from './content.vue'

export default defineComponent({
  name: 'HeaderSolution',
  components: {
    Solution,
  },
  props: {
    popperWrapperClass: {
      type: String,
      default: '',
    },
    // 产线菜单
    lineMenus: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
  },
  emits: {
    solutionMenuClick: (data: any) => typeof data === 'object',
  },
  setup() {
    // 响应式数据
    const selectIndex = ref<number>(0)
    const popoverVisible = ref<boolean>(false)
    const leftPopoverVisible = ref<boolean>(false)

    // 方法
    const popoverShow = (): void => {
      // 为了让关闭动画更流畅 所以设置了两个
      popoverVisible.value = true
      leftPopoverVisible.value = true
    }

    const popoverAfterEnter = (): void => {}

    const popoverHide = (): void => {}

    const PopoverAfterLeave = (): void => {
      popoverVisible.value = false
      leftPopoverVisible.value = false
    }

    const clickSolution = (): void => {
      popoverVisible.value = false
    }

    return {
      selectIndex,
      popoverVisible,
      leftPopoverVisible,
      popoverShow,
      popoverAfterEnter,
      popoverHide,
      PopoverAfterLeave,
      clickSolution,
    }
  },
})
</script>

<style lang="scss" scoped>
.header_left--solution {
  position: relative;
  cursor: pointer;
  width: 68px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--hatech-header-fill-color);

  &.is_active,
  &:active {
    background-color: var(--hatech-header-active-color);
  }

  .icon {
    font-size: 28px;
    color: currentColor;
  }
}
</style>
