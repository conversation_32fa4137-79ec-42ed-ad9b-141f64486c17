<script lang="ts">
import { HatechLayout } from '@hatech/layout_unify'
import { defineComponent } from '@vue/composition-api'
import props from '../../mockData/index.props'

export default defineComponent({
  name: 'App',
  components: {
    HatechLayout,
  },
  setup() {
    const versionStr = 'Vue2'

    return {
      versionStr,
      propsData: props,
    }
  },
})
</script>

<template>
  <div>
    <HatechLayout v-bind="propsData" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
}
</style>
