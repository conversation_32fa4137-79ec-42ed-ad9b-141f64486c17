<script lang="ts">
import { defineComponent } from 'vue-demi'
import props from '../../mockData/index.props'

export default defineComponent({
  name: 'App',
  setup() {
    const versionStr = 'Vue2'

    return {
      versionStr,
      propsData: props,
    }
  },
})
</script>

<template>
  <hatech-layout v-bind="propsData" />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
}
</style>
