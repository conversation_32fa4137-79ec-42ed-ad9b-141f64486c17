<template>
  <div class="header">
    <div class="header_left">
      <slot
        name="header_left"
        :is-show-platform-config="isShowPlatformConfig"
      />
    </div>
    <div class="header_right">
      <template v-for="(menu, index) in renderHeaderColumn">
        <component
          :is="menuTransfer(menu.type)"
          v-if="menu.show && menuTransfer(menu.type)"
          :key="index"
          :menu="menu"
          @on-event="onEvent"
        >
          <template #default="{ visible }">
            <header-menu :menu="menu" :active="visible" />
          </template>
        </component>

        <slot
          v-else-if="menu.type === 'slot'"
          :name="`header_${menu.code}`"
          :menu="menu"
        />

        <header-menu
          v-else-if="menu.show"
          :key="menu.code + index"
          :menu="menu"
          @on-event="onEvent"
        />
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, type PropType } from 'vue-demi'
import type {
  EventData,
  HeaderMenuItem,
  LineMenuItem,
  MenuItem,
} from '../../types/index'
import HeaderDropdown from './components/dropdown.vue'
import HeaderSelect from './components/select.vue'
import HeaderMenu from './menu.vue'

export default defineComponent({
  name: 'HatechHeader',
  components: {
    HeaderMenu,
    HeaderDropdown,
    HeaderSelect,
  },
  props: {
    // 头部功能列
    headerMenus: {
      type: Array as PropType<HeaderMenuItem[]>,
      default: () => [],
    },
    // 当前菜单
    current: {
      type: Object as PropType<MenuItem>,
      default: () => ({}),
    },
    // 产线菜单数据
    lineMenus: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
    // 当前产线索引
    lineIndex: {
      type: Number,
      default: 0,
    },
    // 平台配置状态（替代Bus）
    isPlatformConfig: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
  },
  setup(props, { emit }) {
    // 计算属性
    const isShowPlatformConfig = computed(() => {
      return props.isPlatformConfig || false
    })
    // 方法
    /**
     * 事件透传处理
     */
    const onEvent = (args: EventData): void => {
      emit('onEvent', args)
    }

    /**
     * 组件类型转换
     */
    const menuTransfer = (type: string): string | false => {
      if (!type || type === 'slot') {
        return false
      }
      return `Header${type.replace(type[0], type[0].toUpperCase())}`
    }

    const renderHeaderColumn = computed(() =>
      props.headerMenus.filter((item) => item.code !== 'tenant'),
    )

    return {
      isShowPlatformConfig,
      onEvent,
      menuTransfer,
      renderHeaderColumn,
    }
  },
})
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 100%;
  color: var(--hatech-base-font-color-white);
  display: flex;
  align-items: center;
  background: var(--hatech-base-background-color-routine-head);
}

.header_right {
  flex: 2;
  margin-right: 8px;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  height: 100%;

  .el-dropdown {
    height: 100%;
  }
}
</style>

<style>
.hatech_layout_header_solution_popper {
  padding: 0 !important;
  border: none !important;
  left: 4px !important;
  margin-top: 4px;
}
</style>
