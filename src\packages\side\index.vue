<template>
  <div id="layout_side" onselectstart="return false">
    <el-menu
      :default-active="currPage.name"
      :collapse="collapse"
      unique-opened
      click-to-active
      @select="selectMainMenu"
    >
      <template v-for="(subItem, index) in showMenus">
        <el-menu-item
          v-if="isMenuItem(subItem)"
          :key="index"
          :index="subItem.uiCode"
        >
          <em v-if="subItem.icon" :class="[subItem.icon]" />
          <template #title>
            <span>
              {{ subItem.cnName }}
            </span>
          </template>
        </el-menu-item>
        <el-submenu v-else :key="subItem.uiCode" :index="subItem.uiCode">
          <template #title>
            <em v-if="subItem.icon" :class="[subItem.icon]" />
            <span>{{ subItem.cnName }}</span>
          </template>
          <template v-if="subItem.children && subItem.children.length > 0">
            <template v-for="(route, routeIndex) in subItem.children">
              <el-menu-item
                v-if="route.uiShow === 1"
                :key="`${index}-${routeIndex}`"
                :index="route.uiCode"
                :title="route.cnName"
              >
                {{ route.cnName }}
              </el-menu-item>
            </template>
          </template>
        </el-submenu>
      </template>
    </el-menu>
    <div class="layout_side_built_bottom">
      <CustomMenuItem
        :collapse="collapse"
        :is-render-collapse="!isMultiplexLine"
        @switchCollapse="switchCollapse"
        @click.native="clickPlatformConfig"
      >
        <template v-if="!currIsPlatformConfig">
          <em class="one-icons-setting" title="平台设置" />
          <span class="menu_title"> 平台设置 </span>
        </template>
      </CustomMenuItem>
      <CustomMenuItem
        v-if="isMultiplexLine"
        :collapse="collapse"
        :is-render-collapse="isMultiplexLine"
        @switchCollapse="switchCollapse"
      >
        <HeaderSolution
          popper-wrapper-class="plan_box"
          :line-menus="lineMenus"
          @solutionMenuClick="$emit('solutionMenuClick', $event)"
        >
          <div>
            <em class="one-icons-setting" />
            <span class="menu_title"> 选择解决方案 </span>
          </div>
        </HeaderSolution>
      </CustomMenuItem>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  watch,
  type PropType,
} from '@vue/composition-api'
import HeaderSolution from '../header/solution/index.vue'
import type {
  EventData,
  LineMenuItem,
  MenuItem,
  MenuItem as MenuItemType,
  PageInfo,
} from '../../types/index'
import CustomMenuItem from './customMenuItem.vue'

export default defineComponent({
  name: 'HatechSide',
  components: {
    HeaderSolution,
    CustomMenuItem,
  },
  props: {
    // 总菜单信息
    menus: {
      type: Array as PropType<MenuItemType[]>,
      default: () => [],
    },
    // 没有被结构化的菜单
    originMenus: {
      type: Array as PropType<MenuItemType[]>,
      default: () => [],
    },
    // 当前进入页面
    currPage: {
      type: Object as PropType<PageInfo>,
      default: () => ({}),
    },
    // 上一路由信息
    prevPage: {
      type: Object as PropType<PageInfo>,
      default: () => ({}),
    },
    // 产线菜单
    lineMenus: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
    // 是否浮动
    isFloat: {
      type: Boolean,
      default: false,
    },
    // 是否收起
    collapse: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
    switchCollapse: (value: boolean) => typeof value === 'boolean',
    platformConfigChange: (value: boolean) => typeof value === 'boolean',
    solutionMenuClick: (data: {
      lineIndex: number
      clickItem: MenuItem
      labelItem?: MenuItem
    }) =>
      typeof data === 'object' &&
      typeof data.lineIndex === 'number' &&
      typeof data.clickItem === 'object',
  },
  setup(props, { emit }) {
    // 响应式数据
    const routePaths = ref<string[]>([])
    const currMenu = ref<MenuItemType>()
    const lastCurrMenu = ref<MenuItemType | null>(null)
    const showMenus = ref<MenuItemType[]>([])
    const platformConfigMenus = ref<MenuItemType[]>([])
    const ordinaryMenus = ref<MenuItemType[]>([])
    const currIsPlatformConfig = ref<boolean>(false)

    // 计算属性
    const isMultiplexLine = computed((): boolean => {
      // 是否多产线
      return (props.lineMenus?.length || 0) >= 1
    })
    // 监听器
    // 监听菜单和当前页面变化，计算页面路径
    watch(
      () => props.currPage,
      (newVal: PageInfo | undefined) => {
        if (newVal && props.menus) {
          // 取前一次路由信息（prevPage）的id，然后查找它的子集
          const parentId = props.prevPage?.meta && props.prevPage.meta.id
          const newArray = (props.originMenus || []).filter(
            (item: MenuItemType) => item.parentId === parentId,
          )
          // 找到子集里uiCode相同的元素，再用uiPath去查找
          const newChild = newArray.find(
            (item: MenuItemType) => item.uiCode === newVal.name,
          )
          if (newChild && newChild.uiPath) {
            routePaths.value = getMenuPaths(
              newChild.uiPath,
              props.menus,
              'uiPath',
            ).map((item: MenuItemType) => item.id)
          } else {
            routePaths.value = getMenuPaths(
              newVal.name || (newVal as any).uiCode,
              props.menus,
              'uiCode',
            ).map((item: MenuItemType) => item.id)
          }
        }
      },
      { immediate: true },
    )
    watch(
      () => props.menus,
      (newVal: MenuItemType[] | undefined) => {
        if (newVal && props.currPage) {
          platformConfigMenus.value = []
          ordinaryMenus.value = []
          // 筛选出平台配置菜单
          newVal.forEach((item: MenuItemType) => {
            if (item.uiShow === 1) {
              if (item.uiCode === 'platformConfig') {
                platformConfigMenus.value = item.children || []
              } else {
                ordinaryMenus.value.push(item)
              }
            }
          })
          if (currMenu.value && currMenu.value.uiCode) {
            currMenu.value = newVal.find(
              (menu: MenuItemType) => menu.uiCode === currMenu.value?.uiCode,
            )
          }
          const parentId = props.prevPage?.meta && props.prevPage.meta.id
          const newArray = (props.originMenus || []).filter(
            (item: MenuItemType) => item.parentId === parentId,
          )
          const newChild = newArray.find(
            (item: MenuItemType) => item.uiCode === props.currPage?.name,
          )
          if (newChild && newChild.uiPath) {
            routePaths.value = getMenuPaths(
              newChild.uiPath,
              newVal,
              'uiPath',
            ).map((item: MenuItemType) => item.id)
          } else {
            routePaths.value = getMenuPaths(
              props.currPage?.name || '',
              newVal,
              'uiCode',
            ).map((item: MenuItemType) => item.id)
          }
        }
      },
      { immediate: true },
    )
    watch(
      routePaths,
      (newVal, oldVal) => {
        if (newVal) {
          // 找出当前应该显示的菜单是平台配置还是普通菜单
          if (
            platformConfigMenus.value.some(
              (item: MenuItemType) => item.id === newVal[1],
            )
          ) {
            currIsPlatformConfig.value = true
            showMenus.value = platformConfigMenus.value
            // 替代Bus：通过emit向父组件传递状态变化
            emit('platformConfigChange', true)
          } else {
            currIsPlatformConfig.value = false
            showMenus.value = ordinaryMenus.value
            // 替代Bus：通过emit向父组件传递状态变化
            emit('platformConfigChange', false)
          }
        }
        if (newVal && oldVal) {
          const newMenu = showMenus.value.find((menu: MenuItemType) =>
            newVal.includes(menu.id),
          )
          if (
            newMenu &&
            newMenu.children &&
            newMenu.children.some(
              (item: MenuItemType) => item.uiShow === 1 && item.uiPath,
            )
          ) {
            if (!props.isFloat) {
              currMenu.value = newMenu
            }
            lastCurrMenu.value = newMenu
          } else {
            currMenu.value = undefined
            lastCurrMenu.value = null
          }
        }
      },
      { immediate: true },
    )

    watch(
      showMenus,
      (n, o) => {
        emit('onEvent', {
          event: 'onChangeMenu',
          params: {
            newMenus: n,
            oldMenus: o,
          },
        })
      },
      { immediate: true },
    )

    // 方法定义
    const getCurrPage = (data?: MenuItemType): MenuItemType | false => {
      const func = (menu?: MenuItemType): MenuItemType | false => {
        if (!menu) return false
        if (menu.uiShow === 1 && menu.uiPath) {
          return menu
        } else if (menu.children && menu.children.length) {
          return func(
            menu.children.find(
              (item: MenuItemType) => item.uiShow === 1 && item.uiPath,
            ),
          )
        }
        return false
      }
      return func(data)
    }

    const clickPlatformConfig = (): void => {
      if (currIsPlatformConfig.value) return
      showMenus.value = platformConfigMenus.value
      currIsPlatformConfig.value = true
      // 替代Bus：通过emit向父组件传递状态变化
      emit('platformConfigChange', true)
      const target = getCurrPage(
        platformConfigMenus.value.find(
          (item: MenuItemType) => item.uiShow === 1 && item.uiPath,
        ),
      )
      if (target && target.uiCode) {
        toPage(target.uiCode)
      }
    }
    // 实现getMenuPaths方法
    function getMenuPaths(
      value: string,
      menus: MenuItemType[],
      keyDef: string,
    ): MenuItemType[] {
      let result: MenuItemType[] = []
      let target: MenuItemType | null = null
      ;(function func(menu: any, paths: MenuItemType[] = []) {
        if (target) return false
        if (menu[keyDef] === value) {
          target = menu
          result = paths
        } else if (menu.children && menu.children.length) {
          menu.children.forEach((m: MenuItemType) => func(m, [...paths, m]))
        }
      })(Array.isArray(menus) ? { children: menus } : menus)
      return result
    }

    const toPage = (uiCode: string): void => {
      // 页面跳转逻辑，这里简化处理
      emit('onEvent', {
        event: 'onClickMenu',
        params: {
          code: uiCode,
        },
      })
    }

    const switchCollapse = (value: boolean): void => {
      emit('switchCollapse', value)
    }
    // 点击选择一级菜单
    function selectMainMenu(code: string) {
      const targetCurrMenu = showMenus.value.find(
        (menu) => menu.uiCode === code,
      )

      if (!targetCurrMenu) return
      // 一级菜单无下级菜单时或children中没有路由
      if (
        !targetCurrMenu.children ||
        !targetCurrMenu.children.length ||
        !targetCurrMenu.children.some(
          (item) => item.uiShow === 1 && item.uiPath,
        )
      ) {
        toPage(code)
        currMenu.value = targetCurrMenu
        lastCurrMenu.value = null
      } else if (targetCurrMenu.id) {
        // 点击未选择菜单时
        const target = getCurrPage(
          targetCurrMenu.children.find(
            (item) => item.uiShow === 1 && item.uiPath,
          ),
        )
        currMenu.value = targetCurrMenu
        lastCurrMenu.value = targetCurrMenu
        if (target && target.uiCode) {
          toPage(target.uiCode)
        }
      }
    }
    const toPageEmit = (code: string): void => {
      emit('onEvent', {
        event: 'onClickMenu',
        params: {
          code,
        },
      })
    }

    const isMenuItem = (subItem: MenuItemType): boolean => {
      // 判断是否是菜单项
      return (
        !subItem.children ||
        subItem.children.length === 0 ||
        subItem.children.filter((item: MenuItemType) => item.uiShow === 0)
          .length === subItem.children.length // 如果子菜单全部不显示，则认为是菜单项
      )
    }

    return {
      routePaths,
      currMenu,
      lastCurrMenu,
      showMenus,
      platformConfigMenus,
      ordinaryMenus,
      currIsPlatformConfig,
      isMultiplexLine,
      getCurrPage,
      clickPlatformConfig,
      getMenuPaths,
      toPage,
      switchCollapse,
      toPageEmit,
      isMenuItem,
      selectMainMenu,
    }
  },
})
</script>

<style lang="scss" scoped>
#layout_side {
  height: 100%;
  position: relative;
  z-index: 3;
}
.layout_side_built_bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
}

[class^='one-icons-'] {
  margin-right: 8px;
  text-align: center;
  font-size: 20px;
  vertical-align: middle;
}

.is-collapse {
  [class^='one-icons-'] {
    margin-right: 0;
  }
}

// .custom_menu_item {
//   overflow: hidden;
//   padding-left: 16px;
//   border-radius: 4px;
//   cursor: pointer;
//   height: 40px;
//   margin: 4px 8px;
//   color: var(--menu-item-font-color);
//   font-size: var(--menu-item-font-size);
//   position: relative;
//   line-height: 40px;
//   position: relative;
//   display: flex;
//   align-items: center;
//   &.is-hover:hover {
//     background-color: var(--menu-item-hover-fill);
//   }
// }
// .custom_menu_item + .custom_menu_item {
//   margin-top: 4px;
// }
// .plan_item {
//   padding-left: 0;
//   .plan_box {
//     padding-left: 16px;
//     display: flex;
//     align-items: center;
//     flex: 1;
//     margin-right: 8px;
//     &:hover {
//       background-color: var(--menu-item-hover-fill);
//     }
//   }

//   .collapse_icon {
//     transition: transform 0.3s;
//     width: 32px;
//     height: 32px;
//     text-align: center;
//     &:hover {
//       background-color: var(--menu-item-hover-fill);
//     }
//   }
// }

.is-collapse {
  // .custom_menu_item,
  // .plan_item {
  //   text-align: center;
  //   // width: 44px;
  //   padding-left: 0;
  //   justify-content: center;
  // }
  // .plan_item {
  //   display: flex;
  //   flex-direction: column;
  //   padding-left: 0;
  //   height: 100%;
  //   margin-top: 4px;
  //   .plan_box {
  //     height: 40px;
  //     flex: none;
  //     margin-right: 0;
  //     padding-left: 0;
  //     width: 100%;
  //     justify-content: center;
  //   }
  // }
  .menu_title {
    margin-left: 0px;
    width: 0;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    display: block;
  }
  // .collapse_icon {
  //   margin-top: 4px;
  //   width: 100%;
  //   height: 40px;
  //   transform: rotate(180deg);
  // }
}
</style>
