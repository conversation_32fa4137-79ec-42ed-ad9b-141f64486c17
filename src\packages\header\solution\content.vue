<template>
  <div class="solution-content-view">
    <solution-tabs v-model="localSelectIndex" :tabs-list="newLineMenus">
      <template #search>
        <SearchInput
          v-model="searchValue"
          :menu-list="originalMenuList"
          @search-data="searchEvent"
        />
      </template>
    </solution-tabs>
    <el-scrollbar
      v-if="menuList.length > 0"
      ref="elScroll"
      :style="barStyle.style"
      wrap-style="overflow-x:hidden;"
    >
      <div class="layout-header-menu-content">
        <div v-for="item of menuList" :key="item.id" class="menu-list">
          <div class="menu-label" :title="item.cnName" @click="clickMenu(item)">
            <span class="name">
              {{ item.cnName }}
            </span>
          </div>
          <div class="menu-child-list">
            <div
              v-for="childItem of (item.children || []).filter(
                (i) => i.uiShow === 1,
              )"
              :key="childItem.id"
              class="menu-child-item"
              :title="childItem.cnName"
            >
              <p
                class="name"
                @click="clickMenu(childItem, item)"
                v-html="childItem.showCnName || childItem.cnName"
              />
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div v-else class="no-data" :style="barStyle.style">
      <div>
        暂未找到与 “
        <span class="hatech_header_search_target_font">{{ searchValue }}</span>
        ” 相关的菜单
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  nextTick,
  ref,
  watch,
  type PropType,
} from '@vue/composition-api'
import type { LineMenuItem, MenuItem } from '../../../types/index'
import SearchInput from './search.vue'
import SolutionTabs from './tabs.vue'

export default defineComponent({
  name: 'SolutionContent',
  components: {
    SearchInput,
    SolutionTabs,
  },
  props: {
    lineMenus: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
    },
  },
  emits: {
    input: (value: number) => typeof value === 'number',
    clickSolution: (value: number) => typeof value === 'number',
    solutionMenuClick: (data: {
      lineIndex: number
      clickItem: MenuItem
      labelItem?: MenuItem
    }) =>
      typeof data === 'object' &&
      typeof data.lineIndex === 'number' &&
      typeof data.clickItem === 'object',
  },
  setup(props, { emit }) {
    // 响应式数据
    const localSelectIndex = ref<number>(props.value || 0)
    const searchValue = ref<string>('')
    const newLineMenus = ref<LineMenuItem[]>([])
    const maxHeight = ref<number>(0)
    const platformList = ref<MenuItem[]>([])
    const menuList = ref<MenuItem[]>([])
    const originalMenuList = ref<MenuItem[]>([])
    const paneHeight = ref<number>(540)
    const elScroll = ref<any>()
    // 计算属性
    const barStyle = computed((): { style: { height: string } } => {
      // 内容区域高度
      // 540 面板最大高度 /  65 tabs 高度 / 32 上下间距
      let max = paneHeight.value - 65 - 32

      if (maxHeight.value < max) {
        max = maxHeight.value
      }
      return {
        style: {
          height: `${max}px`,
        },
      }
    })

    // 监听器
    watch(
      () => props.value,
      (val: number | undefined) => {
        localSelectIndex.value = val || 0
      },
      { immediate: true },
    )

    watch(
      () => props.lineMenus,
      (newVal: LineMenuItem[] | undefined) => {
        // 将数据处理一下
        newLineMenus.value = (newVal || []).map((item: LineMenuItem) => {
          const data = {
            ...item,
            platformList: [] as MenuItem[],
            menuList: [] as MenuItem[],
          }
          item.authorityGroupRouteList.forEach((authItem: MenuItem) => {
            if (authItem.uiShow === 1) {
              if (authItem.uiCode === 'platformConfig') {
                data.platformList = authItem.children || []
              } else {
                data.menuList.push(authItem)
              }
            }
          })
          calcMaxHeight(data)
          return data
        })
        getShowListData()
      },
      { immediate: true, deep: true },
    )

    watch(localSelectIndex, () => {
      const elScrollEle = elScroll.value.$el
      if (elScrollEle) {
        // 滚动到顶部
        elScrollEle.wrap.scrollTop = 0
      }
      // 获取展示的菜单数据
      getShowListData()
    })
    // 方法定义
    const clickMenu = (clickItem: MenuItem, labelItem?: MenuItem): void => {
      // 替代Bus：通过emit向父组件传递解决方案菜单点击事件
      emit('solutionMenuClick', {
        lineIndex: localSelectIndex.value,
        clickItem,
        labelItem,
      })
      emitData()
    }

    const emitData = (): void => {
      const index = localSelectIndex.value
      emit('input', index)
      emit('clickSolution', index)
    }
    // 实现calcMaxHeight方法
    function calcMaxHeight(data: any) {
      if (maxHeight.value < paneHeight.value) {
        // 顶部间距
        const menuPanePadding = 32
        // 每个元素的高度，不包括内外间距
        const columnHeight = 22
        // 每行的间距 padding: 9
        const rowPadding = 18
        const rowGap = 10
        // 计算出最大值高度
        let lastNum = 0
        // 如果超过1行，上间距 4
        const menuChildrenMt = 4

        data.menuList.forEach((menuItem: MenuItem, menuIndex: number) => {
          const menuChildren = menuItem.children || []
          // 一共几行
          const row = Math.ceil(menuChildren.length / 7)
          let currentRowHeight = 0
          if (row < 2) {
            currentRowHeight = columnHeight + rowPadding
          } else {
            currentRowHeight =
              (row - 1) * menuChildrenMt + row * columnHeight + rowPadding
          }
          lastNum += currentRowHeight
          if (menuIndex > 0) {
            lastNum += rowGap
          }
        })
        lastNum = lastNum + menuPanePadding
        if (lastNum > maxHeight.value) {
          maxHeight.value = lastNum
        }
      }
    }

    // 实现getShowListData方法
    function getShowListData() {
      platformList.value =
        newLineMenus.value[localSelectIndex.value]?.platformList || []
      menuList.value =
        newLineMenus.value[localSelectIndex.value]?.menuList || []
      originalMenuList.value = menuList.value
      nextTick(() => {
        // 清空搜索条件
        searchValue.value = ''
      })
    }

    function searchEvent(list: MenuItem[]) {
      menuList.value = list
    }

    return {
      localSelectIndex,
      searchValue,
      newLineMenus,
      maxHeight,
      platformList,
      menuList,
      originalMenuList,
      paneHeight,
      barStyle,
      clickMenu,
      emitData,
      calcMaxHeight,
      getShowListData,
      elScroll,
      searchEvent,
    }
  },
})
</script>

<style lang="scss" scoped>
.solution-content-view {
  box-sizing: border-box;
  max-height: 540px;
  position: relative;
  background-color: var(--hatech-base-background-color);
  box-shadow: var(--box-shadow-level-2);

  .layout-header-menu-content {
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .menu-list {
      display: flex;
      width: 100%;
      padding: 9px 8px;
      box-sizing: border-box;

      &:hover {
        background: var(--hatech-base-fill-color-normal);
        border-radius: 2px;
      }

      .menu-label {
        width: 100px;
        margin-right: 16px;
        cursor: pointer;
        height: 22px;
        color: var(--hatech-base-font-color);
        display: flex;
        align-items: center;
        line-height: initial;

        .name {
          font-size: 14px;
          font-weight: 600;
          height: 22px;
          display: block;
          max-width: 96px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:hover {
          color: var(--hatech-primary-color-hover);
        }
      }

      .menu-child-list {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        line-height: initial;

        .menu-child-item {
          width: 84px;
          max-width: 84px;
          border-radius: 2px;
          display: inline-block;
          margin-right: 24px;
          cursor: pointer;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          transition-property: background-color;
          transition-duration: 0.3s;
          transition-timing-function: ease;
          color: var(--hatech-base-font-color-secondary-text);

          &:nth-of-type(n + 8) {
            margin-top: 4px;
          }

          &:nth-of-type(7n + 0) {
            margin-right: 12px;
          }

          .name {
            margin: 0;
            height: 22px;
            line-height: 22px;
            font-size: 14px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          &:hover {
            color: var(--hatech-primary-color-hover);
          }
        }
      }
    }
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 32px;
  }
}
</style>

<style>
.hatech_header_search_target_font {
  color: var(--hatech-base-search-font-color);
}
</style>
