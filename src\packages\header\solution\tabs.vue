<template>
  <div class="header">
    <div ref="solutionListRef" class="solution-list">
      <span v-if="isShowBtn" class="prev" @click="scrollPrev">
        <em class="el-icon-arrow-left" />
      </span>
      <span v-if="isShowBtn" class="next" @click="scrollNext">
        <em class="el-icon-arrow-right" />
      </span>
      <div class="scroll">
        <div class="list" :style="listStyle" @transitionend="transitionend">
          <div
            v-for="(item, index) of tabsList"
            :key="index"
            class="solution-item"
            :class="{
              'is-active': index === activeIndex,
            }"
            @click="selectSolution(index)"
          >
            <span class="name" :title="item.productLineName">
              {{ item.productLineName }}
            </span>
          </div>
        </div>
        <div
          class="line"
          :style="{
            transform: `translateX(${linePos.left})`,
            width: linePos.width,
          }"
        />
      </div>
    </div>
    <slot name="search" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  nextTick,
  onMounted,
  ref,
  watch,
  type PropType,
} from 'vue-demi'
import type { LineMenuItem } from '../../../types/'

export default defineComponent({
  name: 'SolutionTabs',
  model: {
    prop: 'activeIndex',
    event: 'input',
  },
  props: {
    tabsList: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
    activeIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: {
    input: (value: number) => typeof value === 'number',
  },
  setup(props, { emit }) {
    // 响应式数据
    const linePos = ref<{ left: string; width: string }>({
      left: '0px',
      width: '0px',
    })
    const navOffset = ref<number>(0)
    const solutionListRef = ref<HTMLDivElement | null>(null)
    const isShowBtn = ref<boolean>(false)
    const searchValue = ref<string>('')

    // 计算属性
    const listStyle = computed((): { transform: string } => {
      return {
        transform: `translateX(-${navOffset.value}px)`,
      }
    })

    // 方法声明（先声明，后面定义）
    let scrollToActiveTab: () => void

    // 监听器
    watch(
      () => props.activeIndex,
      () => {
        scrollToActiveTab()
      },
      { immediate: true },
    )
    // 方法定义
    const calcLinePosition = (): void => {
      // 获取点击元素的位置， 如果左右切换也需要获取位置
      nextTick(() => {
        if (!solutionListRef.value) return
        const activeElement = solutionListRef.value.querySelector(
          '.solution-item.is-active',
        )
        if (activeElement) {
          const { left, width } = activeElement.getBoundingClientRect()
          // left - 20 是因为padding: 0 16px 以及当前弹框的距离左边4px
          linePos.value = {
            left: `${left - 20}px`,
            width: `${width}px`,
          }
        }
      })
    }

    const selectSolution = (index: number): void => {
      vModelActiveIndex(index)
      if (!isShowBtn.value) {
        calcLinePosition()
      } else {
        scrollToActiveTab()
      }
    }
    const scrollPrev = (): void => {
      const scrollEle = document.querySelector('.scroll') as HTMLElement
      const containerSize = scrollEle?.offsetWidth || 0
      const currentOffset = navOffset.value

      if (!currentOffset) return

      const newOffset =
        currentOffset > containerSize ? currentOffset - containerSize : 0

      navOffset.value = newOffset
    }

    const scrollNext = (): void => {
      const listEle = document.querySelector('.list') as HTMLElement
      const scrollEle = document.querySelector('.scroll') as HTMLElement
      const navSize = listEle?.offsetWidth || 0
      const containerSize = scrollEle?.offsetWidth || 0
      const currentOffset = navOffset.value

      if (navSize - currentOffset <= containerSize) return

      const newOffset =
        navSize - currentOffset > containerSize * 2
          ? currentOffset + containerSize
          : navSize - containerSize

      navOffset.value = newOffset
    }
    // 实现scrollToActiveTab方法
    scrollToActiveTab = (): void => {
      nextTick(() => {
        if (!solutionListRef.value) return
        const listEle = solutionListRef.value.querySelector(
          '.list',
        ) as HTMLElement
        const activeTab = solutionListRef.value.querySelector(
          '.solution-item.is-active',
        )
        const scrollEle = document.querySelector('.scroll') as HTMLElement
        if (activeTab && listEle && scrollEle) {
          const activeTabBounding = activeTab.getBoundingClientRect()
          const navScrollBounding = scrollEle.getBoundingClientRect()
          const maxOffset = listEle.offsetWidth - navScrollBounding.width
          const currentOffset = navOffset.value
          let newOffset = currentOffset

          if (activeTabBounding.left < navScrollBounding.left) {
            newOffset =
              currentOffset - (navScrollBounding.left - activeTabBounding.left)
          }
          if (activeTabBounding.right > navScrollBounding.right) {
            newOffset =
              currentOffset + activeTabBounding.right - navScrollBounding.right
          }
          newOffset = Math.max(newOffset, 0)
          navOffset.value = Math.min(newOffset, maxOffset)
          // 如果计算的偏移量和原来的一样，那么就直接改变选中线的距离
          if (currentOffset === navOffset.value) {
            calcLinePosition()
          }
        }
      })
    }
    const transitionend = (): void => {
      calcLinePosition()
    }

    const vModelActiveIndex = (index: number): void => {
      emit('input', index)
    }

    // 生命周期
    onMounted(() => {
      setTimeout(scrollToActiveTab)
      if (!solutionListRef.value) return

      const scrollEle =
        solutionListRef.value.querySelector<HTMLDivElement>('.scroll')
      const listEle =
        solutionListRef.value.querySelector<HTMLDivElement>('.list')
      if (scrollEle && listEle) {
        isShowBtn.value = listEle.offsetWidth > scrollEle.offsetWidth
      }
    })

    return {
      linePos,
      navOffset,
      solutionListRef,
      isShowBtn,
      searchValue,
      listStyle,
      calcLinePosition,
      selectSolution,
      scrollPrev,
      scrollNext,
      scrollToActiveTab,
      transitionend,
      vModelActiveIndex,
    }
  },
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  margin: 0 16px;
  border-bottom: 1px solid var(--hatech-base-border-color-middle);
}
.solution-list {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;

  .prev,
  .next {
    position: absolute;
    cursor: pointer;
    line-height: 44px;
    font-size: 12px;
    width: 32px;
    height: 100%;
    line-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .prev {
    left: 0;
  }

  .next {
    right: 0;
  }

  .scroll {
    overflow: hidden;
    position: relative;

    .list {
      display: flex;
      white-space: nowrap;
      transition: transform 0.3s;
      float: left;
      z-index: 2;
    }

    .solution-item {
      box-sizing: border-box;
      display: flex;
      justify-items: center;
      align-items: center;
      cursor: pointer;
      position: relative;
      padding: 8px;
      color: var(--hatech-primary-color-special);

      &:first-child {
        margin-left: 0;
      }

      &.is-active .name {
        color: var(--hatech-primary-color);
        font-weight: 600;
      }

      &:hover {
        color: var(--hatech-primary-color-hover);
      }
      .name {
        font-size: 16px;
        font-weight: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 24px;
        margin: 12px 16px;
        color: var(--hatech-base-font-color-secondary-text);
      }
    }

    .line {
      position: absolute;
      bottom: 0;
      height: 2px;
      background: var(--hatech-primary-color);
      border-radius: 1px;
      transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
  }
}
</style>
