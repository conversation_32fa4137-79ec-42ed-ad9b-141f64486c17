/**
 * 组件相关类型定义
 */

// 直接定义基础类型，避免循环引用

/**
 * 头部菜单类型
 */
export type HeaderMenuType = 'dropdown' | 'select' | 'slot' | 'menu'

/**
 * 菜单项基础接口
 */
interface BaseMenuItem {
  id: string
  code: string
  name: string
  cnName: string
  enName?: string
  parentId: string
  uiShow: number
  menuGroupCode: string
  isActionCheck: number
  isWhiteList: number
  isRobotToken: number
  uiPath: string
  uiCode: string
  uiSort: number
  uiTemplate: string
  uiMicroPath: string
  uiContainer: string
  uiIsShowBreadcrumb: number
  uiExtraInfo: string
  icon: string
}

/**
 * 菜单项接口（支持嵌套子菜单）
 */
export interface MenuItem extends BaseMenuItem {
  children?: MenuItem[]
  showCnName?: string
}

/**
 * 页面路由信息
 */
export interface PageInfo {
  name: string
  path: string
  params: Record<string, any>
  meta: {
    id: string
    path: string
    code: string
    params: string | null
    container: string
    microPath: string
    moduleName: string
    uiName: string
    uiIsShowBreadcrumb: number
    isActionCheck: number
    isRobotToken: number
    isWhiteList: number
  }
}

/**
 * 头部菜单类型
 */

/**
 * 头部菜单项接口
 */
export interface HeaderMenuItem {
  code: string
  name?: string
  type: HeaderMenuType
  icon?: string
  show: boolean | number
  badge?: number
  props?: Record<string, any>
}

/**
 * 产线菜单接口
 */
export interface LineMenuItem {
  productLineName: string
  productLineCode: string
  productLineIcon: string
  authorityGroupRouteList: MenuItem[]
  platformList?: MenuItem[]
  menuList?: MenuItem[]
}

/**
 * 下拉菜单选项
 */
export interface DropdownOption {
  /** 选项名称 */
  name: string
  /** 命令值 */
  command: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 选择器选项
 */
export interface SelectOption {
  /** 选项值 */
  value: string
  /** 选项标签 */
  label: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 事件数据接口
 */
export interface EventData {
  event: string
  params: Record<string, any>
}

// ==================== 组件Props接口 ====================

/**
 * HatechLayout 主布局组件Props
 */
export interface HatechLayoutProps {
  /** 侧边栏菜单数据，子菜单使用children字段 */
  menus?: MenuItem[]
  /** 原始菜单数据（未结构化） */
  originMenus?: MenuItem[]
  /** 产线菜单数据 */
  lineMenus?: LineMenuItem[]
  /** 头部功能菜单数据 */
  headerMenus?: HeaderMenuItem[]
  /** 默认选中菜单 */
  defaultMenu?: MenuItem
  /** 当前页面信息 */
  currPage?: PageInfo
  /** 上一页面路由信息 */
  prevPage?: PageInfo
  /** 当前产线索引 */
  lineIndex?: number
}

/**
 * HatechHeader 头部组件Props
 */
export interface HatechHeaderProps {
  /** 头部功能菜单列表 */
  headerMenus?: HeaderMenuItem[]
  /** 当前选中菜单 */
  current?: MenuItem
  /** 产线菜单数据 */
  lineMenus?: LineMenuItem[]
  /** 当前产线索引 */
  lineIndex?: number
}

/**
 * HatechContent 内容组件Props
 */
export interface HatechContentProps {
  /** 内容区域的额外CSS类名 */
  className?: string
  /** 内容区域的样式 */
  style?: Record<string, any>
}

/**
 * HeaderMenu 头部菜单项组件Props
 */
export interface HeaderMenuProps {
  /** 菜单数据 */
  menu: HeaderMenuItem
  /** 是否激活状态 */
  active?: boolean
}

/**
 * HatechSide 侧边栏组件Props
 */
export interface HatechSideProps {
  /** 总菜单信息 */
  menus?: MenuItem[]
  /** 原始菜单数据（未结构化） */
  originMenus?: MenuItem[]
  /** 当前页面信息 */
  currPage?: PageInfo
  /** 上一页面路由信息 */
  prevPage?: PageInfo
  /** 产线菜单数据 */
  lineMenus?: LineMenuItem[]
}

/**
 * CustomMenuItem 自定义菜单项组件Props
 */
export interface CustomMenuItemProps {
  /** 菜单项数据 */
  item: MenuItem
  /** 是否为子菜单项 */
  isChild?: boolean
  /** 菜单层级 */
  level?: number
}

// ==================== 解决方案相关组件Props ====================

/**
 * HeaderSolution 头部解决方案组件Props
 */
export interface HeaderSolutionProps {
  /** 弹出框包装器CSS类名 */
  popperWrapperClass?: string
  /** 产线菜单数据 */
  lineMenus?: LineMenuItem[]
}

/**
 * SolutionContent 解决方案内容组件Props
 */
export interface SolutionContentProps {
  /** 产线菜单数据 */
  lineMenus: LineMenuItem[]
  /** 当前选中的索引 */
  value: number
}

/**
 * SolutionTabs 解决方案标签页组件Props
 */
export interface SolutionTabsProps {
  /** 标签页列表 */
  tabsList: Array<{
    id: string
    cnName: string
    platformList: MenuItem[]
    menuList: MenuItem[]
  }>
  /** 当前激活的索引 */
  activeIndex: number
}

/**
 * SolutionSearch 解决方案搜索组件Props
 */
export interface SolutionSearchProps {
  /** 搜索值 */
  value: string
  /** 菜单列表 */
  menuList: MenuItem[]
}

// ==================== 头部功能组件Props ====================

/**
 * HeaderDropdown 头部下拉菜单组件Props
 */
export interface HeaderDropdownProps {
  /** 菜单数据 */
  menu: HeaderMenuItem
}

/**
 * HeaderSelect 头部选择器组件Props
 */
export interface HeaderSelectProps {
  /** 菜单数据 */
  menu: HeaderMenuItem
}

// ==================== 组件事件接口 ====================

/**
 * 组件通用事件接口
 */
export interface ComponentEvents {
  /** 统一事件处理器 */
  onEvent: (eventData: EventData) => void
}

/**
 * HatechLayout 组件事件接口
 */
export interface HatechLayoutEvents extends ComponentEvents {
  /** 切换折叠状态 */
  switchCollapse?: (collapsed: boolean) => void
}

/**
 * HatechHeader 组件事件接口
 */
export interface HatechHeaderEvents extends ComponentEvents {}

/**
 * HatechSide 组件事件接口
 */
export interface HatechSideEvents extends ComponentEvents {
  /** 切换折叠状态 */
  switchCollapse: (collapsed: boolean) => void
}

/**
 * HeaderMenu 组件事件接口
 */
export interface HeaderMenuEvents extends ComponentEvents {}

/**
 * SolutionContent 组件事件接口
 */
export interface SolutionContentEvents {
  /** 输入事件 */
  input: (value: number) => void
  /** 点击解决方案 */
  clickSolution: (index: number) => void
}

/**
 * SolutionSearch 组件事件接口
 */
export interface SolutionSearchEvents {
  /** 输入事件 */
  input: (value: string) => void
  /** 搜索事件 */
  search: (list: MenuItem[]) => void
}

// ==================== 组件插槽接口 ====================

/**
 * HatechLayout 组件插槽接口
 */
export interface HatechLayoutSlots {
  /** 头部栏上方插槽 */
  before_header?: (props: { props: HatechLayoutProps }) => any
  /** 头部栏插槽 */
  header?: () => any
  /** 侧边栏顶部插槽 */
  layout_side_top?: (props: { props: HatechLayoutProps }) => any
  /** 侧边栏内容插槽 */
  layout_side_content?: () => any
  /** 侧边栏底部插槽 */
  layout_side_bottom?: (props: { props: HatechLayoutProps }) => any
  /** 侧边栏工具插槽 */
  side_tools?: () => any
  /** 主内容区域插槽 */
  content?: () => any
}

/**
 * HatechHeader 组件插槽接口
 */
export interface HatechHeaderSlots {
  /** 头部左侧插槽 */
  header_left?: (props: { isShowPlatformConfig: boolean }) => any
  /** 其他动态头部插槽 */
  [key: string]: ((props: any) => any) | undefined
}

/**
 * HatechSide 组件插槽接口
 */
export interface HatechSideSlots {
  /** 侧边栏工具插槽 */
  side_tools?: () => any
}

/**
 * SolutionTabs 组件插槽接口
 */
export interface SolutionTabsSlots {
  /** 搜索插槽 */
  search?: () => any
}

// ==================== 组件实例接口 ====================

/**
 * Vue组件实例基础接口
 */
export interface BaseComponentInstance {
  /** 组件属性 */
  $props: Record<string, any>
  /** 事件发射器 */
  $emit: (event: string, ...args: any[]) => void
  /** 下一个tick */
  $nextTick: (callback?: () => void) => Promise<void>
  /** 作用域插槽（Vue 2） */
  $scopedSlots?: Record<string, any>
  /** 插槽（Vue 3） */
  $slots?: Record<string, any>
}

/**
 * HatechLayout 组件实例接口
 */
export interface HatechLayoutInstance extends BaseComponentInstance {
  /** 检查设备类型 */
  checkDevice: () => void
  /** 获取当前页面 */
  getCurrPage: (data: any) => MenuItem | null
  /** 点击解决方案菜单 */
  clickSolutionMenu: (params: any) => void
  /** 事件处理 */
  onEvent: (eventData: EventData) => void
}

/**
 * HatechSide 组件实例接口
 */
export interface HatechSideInstance extends BaseComponentInstance {
  /** 菜单选择 */
  menuSelect: (index: string, indexPath: string[], item: any) => void
  /** 跳转页面 */
  toPage: (code: string) => void
  /** 切换折叠状态 */
  switchCollapse: (newCollapse: boolean) => void
  /** 判断是否为菜单项 */
  isMenuItem: (subItem: MenuItem) => boolean
}

// 注意：所有类型都已通过interface/type声明导出，无需额外的export type语句
