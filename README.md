# @hatech/layout_unify

统一鉴权平台布局组件，支持Vue 2.6+、Vue 2.7和Vue 3的多版本兼容。

## 特性

- 🎯 **多版本兼容** - 支持Vue 2.6+、Vue 2.7、Vue 3多版本兼容
- 🎨 **主题系统** - 基于@hatech/theme主题系统，支持统一的视觉风格
- 📱 **响应式设计** - 支持移动端适配，自动检测设备类型
- 🔧 **灵活插槽** - 提供丰富的插槽系统，支持高度自定义
- 🎪 **组件生态** - 包含完整的布局组件：头部、侧边栏、内容区域
- 🚀 **TypeScript** - 完整的TypeScript类型定义支持
- 🔄 **事件系统** - 统一的事件处理机制，便于集成

## 安装

```bash
npm install @hatech/layout_unify
# 或
pnpm add @hatech/layout_unify
# 或
yarn add @hatech/layout_unify
```

## 快速开始

### 全局注册

```javascript
import Vue from 'vue'
import HatechLayoutUnify from '@hatech/layout_unify'

Vue.use(HatechLayoutUnify)
```

### 按需引入

```javascript
import { HatechLayout, HatechHeader, HeaderMenu } from '@hatech/layout_unify'

export default {
  components: {
    HatechLayout,
    HatechHeader,
    HeaderMenu
  }
}
```

### 基础用法

```vue
<template>
  <hatech-layout
    :menus="menus"
    :line-menus="lineMenus"
    :header-menus="headerMenus"
    :curr-page="currPage"
    @onEvent="handleEvent"
  >
    <template #content>
      <router-view />
    </template>
  </hatech-layout>
</template>

<script>
export default {
  data() {
    return {
      menus: [], // 侧边栏菜单数据
      lineMenus: [], // 产线菜单数据
      headerMenus: [], // 头部菜单数据
      currPage: {} // 当前页面信息
    }
  },
  methods: {
    handleEvent(eventData) {
      console.log('事件触发:', eventData)
    }
  }
}
</script>
```

## 组件API

### HatechLayout 主布局组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menus | 侧边栏菜单数据，子菜单使用children字段 | Array | [] |
| originMenus | 原始菜单数据（未结构化） | Array | [] |
| lineMenus | 产线菜单数据 | Array | [] |
| headerMenus | 头部功能菜单数据 | Array | [] |
| defaultMenu | 默认选中菜单 | Object | {} |
| currPage | 当前页面信息 | Object | {} |
| prevPage | 上一页面路由信息 | Object | {} |
| lineIndex | 当前产线索引 | Number | 0 |

#### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onEvent | 统一事件处理器 | (eventData) |

#### Slots

| 插槽名 | 说明 |
|--------|------|
| before_header | 头部栏上方插槽 |
| header | 头部栏插槽 |
| header_left | 头部栏左侧插槽 |
| layout_side_top | 侧边栏顶部插槽 |
| layout_side_content | 侧边栏内容插槽 |
| layout_side_bottom | 侧边栏底部插槽 |
| side_tools | 侧边栏工具插槽 |
| content | 主内容区域插槽 |

### HatechHeader 头部组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| headerMenus | 头部功能菜单列表 | Array | [] |
| current | 当前选中菜单 | Object | {} |
| lineMenus | 产线菜单数据 | Array | [] |
| lineIndex | 当前产线索引 | Number | 0 |

#### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onEvent | 事件处理器 | (eventData) |

#### Slots

| 插槽名 | 说明 |
|--------|------|
| header_left | 头部左侧插槽 |
| header_{code} | 动态头部插槽，{code}为菜单code |

### LayoutSide 侧边栏组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menus | 总菜单信息 | Array | [] |
| originMenus | 原始菜单数据（未结构化） | Array | [] |
| currPage | 当前页面信息 | Object | {} |
| prevPage | 上一页面路由信息 | Object | {} |
| lineMenus | 产线菜单数据 | Array | [] |

#### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onEvent | 事件处理器 | (eventData) |
| switchCollapse | 切换折叠状态 | (collapsed) |

#### Slots

| 插槽名 | 说明 |
|--------|------|
| side_tools | 侧边栏工具插槽 |

### HeaderMenu 头部菜单项组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menu | 菜单数据 | Object | {} |
| active | 是否激活状态 | Boolean | false |

## 数据结构

### 菜单数据结构

```javascript
const menus = [
  {
    id: "7d67942c6ec6085edeb12da0d0a747aa",
    code: "3f5b371b-2912-43ae-b503-7d5094c5fab8",
    name: "全局功能",
    cnName: "全局功能",
    enName: "Global function",
    parentId: "e59e758f493111ec9934005056b34140",
    uiShow: 0,
    menuGroupCode: "istorm-one",
    isActionCheck: 1,
    isWhiteList: 0,
    isRobotToken: 0,
    uiPath: "/global",
    uiCode: "global",
    uiSort: 0,
    uiTemplate: "main",
    uiMicroPath: "",
    uiContainer: "",
    uiIsShowBreadcrumb: 1,
    uiExtraInfo: "",
    icon: "",
    children: [] // 子菜单数组
  }
]
```

### 头部菜单数据结构

```javascript
const headerMenus = [
  {
    code: 'user',
    name: '用户',
    type: 'dropdown', // 类型：dropdown, select, slot
    icon: 'icon-user',
    show: true,
    badge: 0 // 徽章数量
  }
]
```

### 产线菜单数据结构

```javascript
const lineMenus = [
  {
    productLineName: "产线名称",
    productLineCode: "product_line_code",
    productLineIcon: "icon-product",
    authorityGroupRouteList: [], // 权限组路由列表
    platformList: [], // 平台列表
    menuList: [] // 菜单列表
  }
]
```

### 页面信息数据结构

```javascript
const currPage = {
  name: "页面名称",
  path: "/page/path",
  params: {},
  meta: {
    id: "page_id",
    path: "/page/path",
    code: "page_code",
    params: null,
    container: "container_name",
    microPath: "/micro/path",
    moduleName: "module_name",
    uiName: "页面显示名称",
    uiIsShowBreadcrumb: 1,
    isActionCheck: 1,
    isRobotToken: 0,
    isWhiteList: 0
  }
}
```

## 事件系统

组件使用统一的事件系统，所有事件都通过`onEvent`触发：

```javascript
// 事件数据结构
{
  event: 'eventName', // 事件名称
  params: {
    // 事件参数
  }
}
```

### 主要事件类型

- `onClickHeaderMenu`: 点击头部菜单
- `onClickMenu`: 点击侧边栏菜单
- `onClickLine`: 点击产线
- `onChangeMenu`: 切换菜单
- `switchCollapse`: 切换侧边栏折叠状态
- `platformConfigChange`: 平台配置变更
- `solutionMenuClick`: 解决方案菜单点击

### 事件使用示例

```javascript
// 监听所有事件
handleEvent(eventData) {
  const { event, params } = eventData

  switch (event) {
    case 'onClickHeaderMenu':
      console.log('点击头部菜单:', params)
      break
    case 'onClickMenu':
      console.log('点击侧边栏菜单:', params)
      break
    case 'onClickLine':
      console.log('点击产线:', params)
      break
    case 'switchCollapse':
      console.log('切换折叠状态:', params.collapsed)
      break
    default:
      console.log('其他事件:', event, params)
  }
}
```

## 高级用法

### 自定义插槽

```vue
<template>
  <hatech-layout
    :menus="menus"
    :header-menus="headerMenus"
    @onEvent="handleEvent"
  >
    <!-- 头部栏上方插槽 -->
    <template #before_header>
      <div class="custom-banner">自定义横幅</div>
    </template>

    <!-- 头部左侧插槽 -->
    <template #header_left="{ isShowPlatformConfig }">
      <div class="custom-logo">
        <img src="/logo.png" alt="Logo" />
      </div>
    </template>

    <!-- 侧边栏顶部插槽 -->
    <template #layout_side_top>
      <div class="side-header">侧边栏头部</div>
    </template>

    <!-- 侧边栏工具插槽 -->
    <template #side_tools>
      <div class="side-tools">
        <button>工具按钮</button>
      </div>
    </template>

    <!-- 主内容区域 -->
    <template #content>
      <router-view />
    </template>
  </hatech-layout>
</template>
```

### 响应式布局

组件会自动检测设备类型并应用相应的样式：

```javascript
// 组件内部会自动处理移动端适配
// 当检测到移动设备时，会添加 'is-mobile' 类名
// 可以通过CSS自定义移动端样式

.layout-container.is-mobile {
  /* 移动端样式 */
}
```

## 开发

### 环境要求

- Node.js >= 16
- pnpm >= 7

### 开发命令

```bash
# 安装依赖
pnpm install

# Vue 2 开发
pnpm dev:2

# Vue 2.7 开发
pnpm dev:2.7

# Vue 3 开发
pnpm dev:3

# 构建
pnpm build

# 测试
pnpm test:all

# 代码检查
pnpm lint
```

### 项目结构

```
├── src/                    # 源码目录
│   ├── packages/          # 组件源码
│   │   ├── layout/        # 主布局组件
│   │   ├── header/        # 头部组件
│   │   │   ├── components/    # 头部子组件
│   │   │   └── solution/      # 解决方案相关组件
│   │   └── side/          # 侧边栏组件
│   ├── types/             # TypeScript类型定义
│   └── index.ts           # 入口文件
├── playground/            # 示例项目
│   └── vue2/             # Vue 2 示例
├── __test__/             # 测试文件
├── mockData/             # 模拟数据
├── dist/                 # 构建输出
└── scripts/              # 构建脚本
```

## TypeScript 支持

本组件库提供完整的TypeScript类型定义，包括：

- 组件Props接口
- 事件回调类型
- 数据结构接口
- 插槽类型定义

```typescript
import type {
  HatechLayoutProps,
  HatechHeaderProps,
  MenuItem,
  HeaderMenuItem,
  LineMenuItem,
  EventData
} from '@hatech/layout_unify'
```

## 浏览器兼容性

- Vue 2.6+: 支持现代浏览器和IE11+
- Vue 2.7+: 支持现代浏览器和IE11+
- Vue 3: 支持现代浏览器

## 依赖说明

### 核心依赖
- `vue-demi`: Vue 2/3 兼容层
- `@hatech/element-ui`: 基础UI组件库
- `@hatech/theme`: 主题系统
- `@one-public/icons`: 图标库

### 开发依赖
- `typescript`: TypeScript支持
- `vite`: 构建工具
- `vitest`: 测试框架

## 许可证

ISC

## 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的更新记录。

## 技术支持

- 作者: duowb
- 邮箱: <EMAIL>
- 仓库: http://***********:8888/hatech-web/hatech-web-layout-unify.git

## 模板

使用[此模板](https://github.com/ChuHoMan/vue-demi-component-template)用来兼容Vue 2、Vue 2.7、Vue 3
