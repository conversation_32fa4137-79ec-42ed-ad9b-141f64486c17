# @hatech/layout_unify

统一鉴权平台布局组件，支持Vue 2.6+、Vue 2.7和Vue 3的多版本兼容。

## 特性

- 🎯 支持Vue 2.6+、Vue 2.7、Vue 3多版本兼容
- 🎨 基于@hatech/theme主题系统
- 📱 响应式布局设计
- 🔧 灵活的插槽系统
- 🎪 丰富的组件生态

## 安装

```bash
npm install @hatech/layout_unify
# 或
pnpm add @hatech/layout_unify
# 或
yarn add @hatech/layout_unify
```

## 快速开始

### 全局注册

```javascript
import Vue from 'vue'
import HatechLayoutUnify from '@hatech/layout_unify'

Vue.use(HatechLayoutUnify)
```

### 按需引入

```javascript
import { HatechLayout, HatechHeader, HatechContent } from '@hatech/layout_unify'

export default {
  components: {
    HatechLayout,
    HatechHeader,
    HatechContent
  }
}
```

### 基础用法

```vue
<template>
  <hatech-layout
    :menus="menus"
    :line-menus="lineMenus"
    :header-menus="headerMenus"
    :curr-page="currPage"
    @onEvent="handleEvent"
  >
    <template #content>
      <router-view />
    </template>
  </hatech-layout>
</template>

<script>
export default {
  data() {
    return {
      menus: [], // 侧边栏菜单数据
      lineMenus: [], // 产线菜单数据
      headerMenus: [], // 头部菜单数据
      currPage: {} // 当前页面信息
    }
  },
  methods: {
    handleEvent(eventData) {
      console.log('事件触发:', eventData)
    }
  }
}
</script>
```

## 组件API

### HatechLayout 主布局组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menus | 侧边栏菜单数据，子菜单使用children字段 | Array | [] |
| originMenus | 原始菜单数据（未结构化） | Array | [] |
| lineMenus | 产线菜单数据 | Array | [] |
| headerMenus | 头部功能菜单数据 | Array | [] |
| defaultMenu | 默认选中菜单 | Object | {} |
| currPage | 当前页面信息 | Object | {} |
| prevPage | 上一页面路由信息 | Object | {} |
| lineIndex | 当前产线索引 | Number | 0 |

#### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onEvent | 统一事件处理器 | (eventData) |

#### Slots

| 插槽名 | 说明 |
|--------|------|
| before_header | 头部栏上方插槽 |
| header | 头部栏插槽 |
| header_left | 头部栏左侧插槽 |
| layout_side_top | 侧边栏顶部插槽 |
| layout_side_content | 侧边栏内容插槽 |
| layout_side_bottom | 侧边栏底部插槽 |
| side_tools | 侧边栏工具插槽 |
| content | 主内容区域插槽 |

### HatechHeader 头部组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| headerMenus | 头部功能菜单列表 | Array | [] |
| current | 当前选中菜单 | Object | {} |
| lineMenus | 产线菜单数据 | Array | [] |
| lineIndex | 当前产线索引 | Number | 0 |

#### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onEvent | 事件处理器 | (eventData) |

#### Slots

| 插槽名 | 说明 |
|--------|------|
| header_left | 头部左侧插槽 |
| header_{code} | 动态头部插槽，{code}为菜单code |

### HatechContent 内容组件

简单的内容容器组件，提供默认插槽用于放置主要内容。

### HeaderMenu 头部菜单项组件

#### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| menu | 菜单数据 | Object | {} |
| active | 是否激活状态 | Boolean | false |

## 数据结构

### 菜单数据结构

```javascript
待补充
```

### 头部菜单数据结构

```javascript
const headerMenus = [
  {
    code: 'user',
    name: '用户',
    type: 'dropdown', // 类型：dropdown, select, slot
    icon: 'icon-user',
    show: true,
    badge: 0 // 徽章数量
  }
]
```

### 产线菜单数据结构

```javascript
待补充...
```

## 事件系统

组件使用统一的事件系统，所有事件都通过`onEvent`触发：

```javascript
// 事件数据结构
{
  event: 'eventName', // 事件名称
  params: {
    // 事件参数
  }
}
```

### 主要事件类型

- `onClickHeaderMenu`: 点击头部菜单
- `onClickMenu`: 点击侧边栏菜单
- `onClickLine`: 点击产线
- `onChangeMenu`: 切换菜单

## 开发

### 环境要求

- Node.js >= 16
- pnpm >= 7

### 开发命令

```bash
# 安装依赖
pnpm install

# Vue 2 开发
pnpm dev:2

# Vue 2.7 开发
pnpm dev:2.7

# Vue 3 开发
pnpm dev:3

# 构建
pnpm build

# 测试
pnpm test:all

# 代码检查
pnpm lint
```

### 项目结构

```
├── packages/           # 组件源码
│   ├── layout/        # 主布局组件
│   ├── header/        # 头部组件
│   ├── content/       # 内容组件
│   ├── side/          # 侧边栏组件
│   └── mixins/        # 混入
├── playground/        # 示例项目
│   ├── vue2/         # Vue 2 示例
│   ├── vue2.7/       # Vue 2.7 示例
│   └── vue3/         # Vue 3 示例
├── tests/            # 测试文件
└── src/              # 入口文件
```

## 许可证

ISC

## 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的更新记录。


## 模板
使用[此模板](https://github.com/ChuHoMan/vue-demi-component-template)用来兼容，vue2,vue2.7, vue3
