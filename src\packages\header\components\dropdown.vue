<template>
  <el-dropdown
    v-bind="dropdownProps"
    class="header_dropdown"
    @command="onCommand"
    @visible-change="onVisibleChange"
  >
    <slot :visible="visible" />
    <template #dropdown>
      <el-dropdown-menu :class="['hatech_header_dropdown_menu', { isTheme }]">
        <el-dropdown-item
          v-for="(item, index) in items"
          :key="index"
          v-bind="item"
        >
          <template v-if="isTheme">
            <span
              :class="[
                'theme_icon',
                item.command,
                item.command === 'black' ? 'one-icons-moon' : 'one-icons-sun',
              ]"
            />
            <span class="theme_text">{{ item.name }}</span>
          </template>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script lang="ts">
import { computed, defineComponent, ref, type PropType } from 'vue-demi'
import type { DropdownOption, EventData, HeaderMenuItem } from '../../../types/'

export default defineComponent({
  name: 'HeaderMenuDropdown',
  props: {
    menu: {
      type: Object as PropType<HeaderMenuItem>,
      default: () => ({}),
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
  },
  setup(props, { emit }) {
    // 响应式数据
    const visible = ref<boolean>(false)

    // 计算属性
    const items = computed((): DropdownOption[] => {
      const { options = [] } = props.menu?.props || {}
      return options as DropdownOption[]
    })

    const dropdownProps = computed((): Record<string, any> => {
      const { options, ...ddProps } = props.menu?.props || {}
      return ddProps
    })

    const isTheme = computed((): boolean => {
      return props.menu?.code === 'theme'
    })

    // 方法
    const onCommand = (command: string): void => {
      const item = items.value.find(
        (i: DropdownOption) => i.command === command,
      )
      const eventData: EventData = {
        event: 'onClickHeaderMenu',
        params: {
          value: command,
          menu: props.menu,
          item,
        },
      }
      emit('onEvent', eventData)
    }

    const onVisibleChange = (visibleState: boolean): void => {
      visible.value = visibleState
    }

    return {
      visible,
      items,
      dropdownProps,
      isTheme,
      onCommand,
      onVisibleChange,
    }
  },
})
</script>

<style lang="scss">
.theme_box {
  width: 12px;
  height: 12px;
  display: inline-block;
  box-sizing: border-box;
  border-radius: 1px;
  margin-right: 8px;

  &.white {
    border: 1px solid #e5e6eb;
    background-color: #ffffff;
  }
  &.black {
    border: 1px solid #262b3b;
    background-color: #101114;
  }
}
</style>

<style lang="scss">
.hatech_header_dropdown_menu {
  border: var(--border-base);
  box-shadow: var(--box-shadow-level-1);
  border-radius: 8px;
  &.isTheme {
    padding: 8px 4px;
    .el-dropdown-menu__item {
      line-height: initial;
      margin: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px 8px;
      gap: 8px;
      .theme_icon {
        font-size: 16px;
        &.white {
          color: #f9c626;
        }
        &.black {
          color: #1d2129;
        }
      }
      .theme_text {
        font-size: 14px;
        height: 22px;
        font-size: 14px;
        font-weight: 350;
        line-height: 22px;
      }
    }
  }
}
</style>
