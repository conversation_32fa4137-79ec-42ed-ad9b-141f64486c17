<template>
  <div class="layout-container" :class="{ 'is-mobile': isMobile }">
    <div :class="['layout-container__side', { 'is-collapse': isCollapse }]">
      <div class="layout-container__side-top">
        <slot name="layout_side_top" :props="$props" />
      </div>
      <div class="layout-container__side-content">
        <slot name="layout_side_content">
          <layout-side
            :line-menus="lineMenus"
            :menus="useMenus"
            :origin-menus="originMenus"
            :curr-page="currPage"
            :prev-page="prevPage"
            @on-event="onEvent"
            @switch-collapse="switchCollapse"
            @platform-config-change="handlePlatformConfigChange"
            @solution-menu-click="clickSolutionMenu"
          >
            <template #side_tools>
              <slot name="side_tools" />
            </template>
          </layout-side>
        </slot>
      </div>
      <div class="layout-container__side-bottom">
        <slot name="layout_side_bottom" :props="$props" />
      </div>
    </div>
    <div class="layout-container__right">
      <div class="layout-container__header">
        <!-- 头部栏上部插槽 -->
        <slot name="before_header" :props="$props" />
        <slot name="header">
          <hatech-header
            :line-menus="lineMenus"
            :header-menus="headerMenus"
            :current="currHeaderMenu"
            :line-index="lineIndex"
            :is-platform-config="isPlatformConfig"
            @on-event="onEvent"
          >
            <template v-for="(_, slotName) in $slots" #[slotName]="context">
              <slot
                v-if="/^header_/.test(slotName.toString())"
                :name="slotName"
                v-bind="context"
              />
            </template>
          </hatech-header>
        </slot>
      </div>
      <div class="layout-container__content">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  onMounted,
  provide,
  ref,
  watch,
  type PropType,
} from 'vue-demi'
import HatechHeader from '../header/index.vue'
import LayoutSide from '../side/index.vue'
import type {
  EventData,
  HeaderMenuItem,
  LineMenuItem,
  MenuItem,
  PageInfo,
} from '../../types/index'

export default defineComponent({
  name: 'HatechLayout',
  components: { HatechHeader, LayoutSide },
  props: {
    // 产线菜单
    lineMenus: {
      type: Array as PropType<LineMenuItem[]>,
      default: () => [],
    },
    // 菜单信息，包括所有菜单信息，子菜单使用children字段
    menus: {
      type: Array as PropType<MenuItem[]>,
      default: () => [],
    },
    // 没有被结构化的菜单
    originMenus: {
      type: Array as PropType<MenuItem[]>,
      default: () => [],
    },
    // 默认菜单
    defaultMenu: {
      type: Object as PropType<MenuItem>,
      default: () => ({}),
    },
    // 当前页面
    currPage: {
      type: Object as PropType<PageInfo>,
      default: () => ({}),
    },
    // 上一路由信息
    prevPage: {
      type: Object as PropType<PageInfo>,
      default: () => ({}),
    },
    // 头部按钮
    headerMenus: {
      type: Array as PropType<HeaderMenuItem[]>,
      default: () => [],
    },
    lineIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
  },
  setup(props, { emit }) {
    // 响应式数据
    const currHeaderMenu = ref<MenuItem>()
    const useLineMenu = ref<LineMenuItem | null>(null) // 当前使用的产线菜单
    const isMobile = ref<boolean>(false)
    const isCollapse = ref<boolean>(false)
    // 新增：平台配置状态，替代Bus
    const isPlatformConfig = ref<boolean>(false)

    // 祖孙组件传值 发送originMenus
    provide('originMenus', props.originMenus)

    // 计算属性
    const useMenus = computed((): MenuItem[] => {
      let menus: MenuItem[] = []
      if (useLineMenu.value) {
        menus = useLineMenu.value.authorityGroupRouteList
      } else if (props.menus) {
        menus = props.menus
      }
      return menus
    })

    const checkDevice = (): void => {
      isMobile.value = window.innerWidth <= 768
    }

    // 监听器
    watch(
      () => props.defaultMenu,
      (newVal: MenuItem | undefined) => {
        currHeaderMenu.value = newVal
      },
    )

    watch(
      () => props.lineMenus,
      (value: LineMenuItem[] | undefined) => {
        useLineMenu.value = (value || [])[props.lineIndex || 0] || null
      },
      { immediate: true, deep: true },
    )

    // 生命周期
    onMounted(() => {
      currHeaderMenu.value = props.defaultMenu
      checkDevice()
      window.addEventListener('resize', checkDevice)
      console.log('created layout_unify')
    })

    onBeforeUnmount(() => {
      window.removeEventListener('resize', checkDevice)
    })

    const filterMenus = (menus: MenuItem[]): MenuItem[] => {
      if (menus && menus.length > 0) {
        const newMenus = JSON.parse(
          JSON.stringify(
            menus.filter((item: MenuItem) => item.uiPath && item.uiShow !== 0),
          ),
        )
        newMenus.forEach((item: MenuItem) => {
          if (item.children && item.children.length > 0) {
            item.children = filterMenus(item.children)
          }
        })
        return newMenus
      }
      return []
    }

    /**
     * 事件处理
     */
    const onEvent = (args: EventData): void => {
      emit('onEvent', args)
    }

    const switchCollapse = (isCollapseValue: boolean): void => {
      isCollapse.value = isCollapseValue
    }

    /**
     * 点击头部一级菜单后
     */
    const onClickHeaderMenu = (menu: MenuItem): void => {
      currHeaderMenu.value = menu
    }

    /**
     * 处理平台配置状态变化（替代Bus事件）
     */
    const handlePlatformConfigChange = (
      isPlatformConfigValue: boolean,
    ): void => {
      isPlatformConfig.value = isPlatformConfigValue
    }

    /**
     * 点击产线触发的事件（替代Bus事件）
     */
    const clickSolutionMenu = (params: {
      lineIndex: number
      clickItem: MenuItem
      labelItem?: MenuItem
      platformList?: MenuItem[]
    }): void => {
      const { lineIndex, clickItem, labelItem, platformList } = params
      useLineMenu.value = (props.lineMenus || [])[lineIndex]

      let code: string | undefined
      if (platformList) {
        // 点击的是平台配置
        const data = platformList.find(
          (item: MenuItem) => item.uiShow === 1 && item.uiPath,
        )
        const target = getCurrPage(data)
        if (target) {
          code = target.uiCode
        }
      } else if (labelItem) {
        // 点击的是产线面板二级菜单
        if (clickItem.uiShow === 1 && clickItem.uiPath) {
          code = clickItem.uiCode
        } else if (Array.isArray(clickItem.children)) {
          const data = clickItem.children.find(
            (item) => item.uiShow === 1 && item.uiPath,
          )
          const target = getCurrPage(data)
          if (target) {
            code = target.uiCode
          }
        }
      } else if (!Array.isArray(clickItem.children)) {
        // 如果没有子菜单，则直接跳转到相应的页面
        code = clickItem.uiCode
      } else {
        // 如果有子菜单，就跳转到子菜单可以打开的第一个页面
        const data = clickItem.children.find(
          (item: MenuItem) => item.uiShow === 1 && item.uiPath,
        )
        if (data) {
          const target = getCurrPage(data)
          if (target) {
            code = target.uiCode
          }
        } else {
          // 如果有子菜单，但是都是二级页面，则直接跳转到父级页面
          code = clickItem.uiCode
        }
      }
      if (!code) {
        console.warn('no code')
      }
      emit('onEvent', {
        event: 'onClickMenu',
        params: {
          code,
        },
      })
      emit('onEvent', {
        event: 'onClickLine',
        params: {
          index: lineIndex,
          lineList: props.lineMenus,
        },
      })
    }

    const getCurrPage = (data: MenuItem | undefined): MenuItem | null => {
      const func = (menu: MenuItem | undefined): MenuItem | null => {
        if (!menu) return null
        if (menu.uiShow === 1 && menu.uiPath) {
          return menu
        } else if (menu.children && menu.children.length) {
          return func(
            menu.children.find(
              (item: MenuItem) => item.uiShow === 1 && item.uiPath,
            ),
          )
        }
        return null
      }
      return func(data)
    }

    return {
      currHeaderMenu,
      useLineMenu,
      isMobile,
      isCollapse,
      isPlatformConfig,
      useMenus,
      checkDevice,
      filterMenus,
      onEvent,
      switchCollapse,
      onClickHeaderMenu,
      handlePlatformConfigChange,
      clickSolutionMenu,
      getCurrPage,
    }
  },
})
</script>

<style lang="scss" scoped>
.layout-container {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  background: var(--hatech-base-background-color);
  color: var(--hatech-base-font-color);
  display: flex;

  .layout-container__side {
    height: 100%;
    width: 208px;
    background: var(--hatech-base-fill-color-nav-hover);
    display: flex;
    flex-direction: column;
    &.is-collapse {
      width: 60px;
    }
  }
  .layout-container__side-top {
    height: 56px;
  }
  .layout-container__side-content {
    flex: 1;
  }

  .layout-container__right {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .layout-container__header {
    height: 48px;
    background: var(--hatech-base-fill-color-nav-hover);
  }

  .layout-container__content {
    flex: 1;
    width: 100%;
    height: 100%;
  }

  &.is-mobile {
    .layout-container__header,
    .hatech-breadcurmb,
    .layout-container__side {
      display: none;
    }

    .layout-container__content {
      width: 100%;
      height: 100%;
    }
    .layout_content.breadcrumb {
      height: 100%;
      background: var(--hatech-base-fill-color-nav-hover);
    }
  }
}
</style>
