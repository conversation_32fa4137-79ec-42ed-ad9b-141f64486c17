<script lang="ts">
import { defineComponent } from 'vue-demi'
export default defineComponent({
  name: 'CustomMenuItem',
  props: {
    isRenderCollapse: Boolean,
    collapse: Boolean,
  },
  emits: {
    switchCollapse: (value: boolean) => typeof value === 'boolean',
  },
  setup(props, { emit }) {
    const switchCollapse = (): void => {
      emit('switchCollapse', !props.collapse)
    }
    return {
      switchCollapse,
    }
  },
})
</script>

<template>
  <div :class="['custom_menu_item', { is_right: isRenderCollapse }]">
    <div class="left_box">
      <slot />
    </div>
    <div
      v-if="isRenderCollapse"
      class="right_box"
      :title="collapse ? '展开' : '收起'"
      @click="switchCollapse"
    >
      <em class="one-icons-menu_unfold" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom_menu_item {
  overflow: hidden;
  padding-left: 16px;
  border-radius: 4px;
  cursor: pointer;
  height: 40px;
  margin: 4px 8px;
  color: var(--menu-item-font-color);
  font-size: var(--menu-item-font-size);
  position: relative;
  line-height: 40px;
  position: relative;
  display: flex;
  align-items: center;

  &:hover {
    background-color: var(--menu-item-hover-fill);
  }

  &.is_right {
    &:hover {
      background-color: transparent;
    }
  }
}

.is_right {
  padding-left: 0;
  .left_box {
    padding-left: 16px;
    display: flex;
    align-items: center;
    flex: 1;
    &:hover {
      background-color: var(--menu-item-hover-fill);
    }
  }

  .right_box {
    transition: transform 0.3s;
    width: 32px;
    height: 32px;
    text-align: center;
    &:hover {
      background-color: var(--menu-item-hover-fill);
    }
  }
}

.is-collapse {
  .custom_menu_item {
    text-align: center;
    padding-left: 0;
    justify-content: center;
  }
  .is_right {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    height: 100%;
    margin-top: 4px;
    .left_box {
      height: 40px;
      flex: none;
      margin-right: 0;
      padding-left: 0;
      width: 100%;
      justify-content: center;
    }
    .right_box {
      margin-top: 4px;
      width: 100%;
      height: 40px;
      transform: rotate(180deg);
    }
  }
}

.custom_menu_item + .custom_menu_item {
  margin-top: 4px;
}
</style>
