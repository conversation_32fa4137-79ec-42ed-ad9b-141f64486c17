<template>
  <el-select v-bind="selectProps" class="ml12 header_select" @change="onChange">
    <el-option
      v-for="(item, index) in items"
      :key="index"
      :title="item.label"
      v-bind="item"
    />
  </el-select>
</template>

<script lang="ts">
import { computed, defineComponent, type PropType } from 'vue-demi'
import type { EventData, HeaderMenuItem, SelectOption } from '../../../types/'

export default defineComponent({
  name: 'HeaderSelect',

  props: {
    menu: {
      type: Object as PropType<HeaderMenuItem>,
      default: () => ({}),
    },
  },
  emits: {
    onEvent: (eventData: EventData) =>
      typeof eventData === 'object' && eventData.event,
  },
  setup(props, { emit }) {
    // 计算属性
    const items = computed((): SelectOption[] => {
      const { options = [] } = props.menu?.props || {}
      return options as SelectOption[]
    })

    const selectProps = computed((): Record<string, any> => {
      const { options, ...selectPropsData } = props.menu?.props || {}
      return selectPropsData
    })

    // 方法
    const onChange = (value: string): void => {
      const item = items.value.find(
        (item: SelectOption) => item.value === value,
      )
      const eventData: EventData = {
        event: 'onClickHeaderMenu',
        params: {
          value,
          menu: props.menu,
          item,
        },
      }
      emit('onEvent', eventData)
    }

    return {
      items,
      selectProps,
      onChange,
    }
  },
})
</script>

<style lang="scss" scoped>
.ml12 {
  margin-left: 12px;
}
.header_select {
  color: var(--hatech-base-font-color-white);
  ::v-deep .el-input__inner {
    background-color: transparent;
    color: currentColor;
  }
  ::v-deep .el-input .el-select__caret.el-icon-arrow-up {
    color: var(--hatech-base-border-color);
  }
}
</style>
